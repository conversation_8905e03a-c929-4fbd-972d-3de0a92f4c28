// Mock data for carpool endpoints - using string literal for BookingStatus to avoid import issues
type BookingStatus = 'pending' | 'approved' | 'rejected' | 'completed'

// Mock data for carpool endpoints
export const mockPurposeOfRequestOptions = [
  { id: 1, label: 'Official Business' },
  { id: 2, label: 'Training' },
  { id: 3, label: 'Meeting' },
  { id: 4, label: 'Site Visit' },
]

export const mockLocationOptions = [
  { id: 1, label: 'Main Office' },
  { id: 2, label: 'Branch Office' },
  { id: 3, label: 'Warehouse' },
  { id: 4, label: 'Training Center' },
]

export const mockVehicleTypes = [
  { id: 1, label: 'Sedan' },
  { id: 2, label: 'SUV' },
  { id: 3, label: '<PERSON>' },
  { id: 4, label: 'Truck' },
]

export const mockVehicles = [
  { id: 1, registration: 'ABC123', type: 'Sedan' },
  { id: 2, registration: 'DEF456', type: 'SUV' },
  { id: 3, registration: 'GHI789', type: '<PERSON>' },
]

export const mockDrivers = [
  { id: 1, name: '<PERSON>' },
  { id: 2, name: '<PERSON>' },
  { id: 3, name: '<PERSON>' },
]

export const mockEquipments = [
  { id: 1, name: 'Laptop' },
  { id: 2, name: 'Projector' },
  { id: 3, name: 'Camera' },
]

export const mockUsers = [
  { id: 1, name: 'Alice Brown', username: 'alice.brown' },
  { id: 2, name: '<PERSON> <PERSON>', username: 'charlie.wilson' },
]

export const mockBookingData = {
  id: 1,
  purposeOfRequest: 1,
  requestDescription: 'Test booking description',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: 1,
  vehicleId: 1,
  numberOfPassengers: 3,
  driverSelection: 'specific' as const,
  driverId: 1,
  vehicleCommanderSelection: 'self' as const,
  vehicleCommanderId: null,
  pickupTime: '2024-01-15T09:00:00.000Z',
  pickupLocation: 1,
  dropoffTime: '2024-01-15T17:00:00.000Z',
  journeyType: 'one-way' as const,
  equipmentType: 'car-boot' as const,
  equipmentIds: [1],
  remarks: 'Test remarks',
  status: 'pending' as BookingStatus,
}

// Endpoint mock functions
export const carpoolEndpointMocks = {
  // Get purpose of request options
  getPurposeOfRequestOptions: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockPurposeOfRequestOptions,
    },
  }),

  // Get location options
  getLocationOptions: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockLocationOptions,
    },
  }),

  // Get vehicle types
  getVehicleTypes: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockVehicleTypes,
    },
  }),

  // Get vehicles
  getVehicles: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockVehicles,
    },
  }),

  // Get drivers
  getDrivers: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockDrivers,
    },
  }),

  // Get equipments
  getEquipments: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockEquipments,
    },
  }),

  // Get users
  getUsers: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockUsers,
    },
  }),

  // Create booking
  createBooking: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { success: true, bookingId: 1 },
    },
  }),

  // Update booking
  updateBooking: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { success: true },
    },
  }),

  // Get booking details
  getBookingDetails: () => ({
    delay: 50,
    body: {
      id: 10,
      result: mockBookingData,
    },
  }),

  // Approve booking
  approveBooking: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { success: true },
    },
  }),

  // Reject booking
  rejectBooking: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { success: true },
    },
  }),
}
