// Mock data for carpool endpoints - using string literal for BookingStatus to avoid import issues
type BookingStatus = 'pending' | 'approved' | 'rejected' | 'completed'

export const mockBookingData = {
  id: 1,
  purposeOfRequest: 1,
  requestDescription: 'Test booking description',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: 1,
  vehicleId: 1,
  numberOfPassengers: 3,
  driverSelection: 'specific' as const,
  driverId: 1,
  vehicleCommanderSelection: 'self' as const,
  vehicleCommanderId: null,
  pickupTime: '2024-01-15T09:00:00.000Z',
  pickupLocation: 1,
  dropoffTime: '2024-01-15T17:00:00.000Z',
  journeyType: 'one-way' as const,
  equipmentType: 'car-boot' as const,
  equipmentIds: [1],
  remarks: 'Test remarks',
  status: 'pending' as BookingStatus,
}

// Endpoint mock functions
export const carpoolEndpointMocks = {
  ct_fleet_get_available_vehicles: () => ({
    body: {
      id: 10,
      result: [
        { id: 1, registration: 'ABC123', vehicleTypeId: 1 },
        { id: 2, registration: 'DEF456', vehicleTypeId: 2 },
      ],
    },
  }),
  ct_fleet_get_booking_options: () => ({
    body: {
      id: 10,
      result: {
        equipments: [
          { id: 1, name: 'Laptop' },
          { id: 2, name: 'Projector' },
        ],
        drivers: [
          { id: 1, name: 'John Doe' },
          { id: 2, name: 'Jane Smith' },
        ],
      },
    },
  }),
  ct_fleet_get_additional_locations: () => ({
    body: {
      id: 10,
      result: [
        { id: 1, locationName: 'Additional Location 1' },
        { id: 2, locationName: 'Additional Location 2' },
      ],
    },
  }),
}
