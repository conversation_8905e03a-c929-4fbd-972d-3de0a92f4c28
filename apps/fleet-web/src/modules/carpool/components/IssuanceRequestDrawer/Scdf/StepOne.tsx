import { useMemo } from 'react'
import {
  Autocomplete,
  Box,
  Checkbox,
  FormControlLabel,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { Controller, useWatch, type Control } from 'react-hook-form'

import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import UserAutocompleteRHF from 'src/modules/shared/UserAutocompleteRHF'

import { ctIntl } from 'cartrack-ui-kit'
import type { BookingFormSchema } from './schema'
import type { BookingPurposeAutocompleteOption } from './types'

type StepOneProps = {
  control: Control<BookingFormSchema>
  bookingPurposeOptions: {
    array: Array<BookingPurposeAutocompleteOption>
    byId: Map<number, BookingPurposeAutocompleteOption>
  }
  disabled: boolean
}

const StepOne = ({ control, bookingPurposeOptions, disabled }: StepOneProps) => {
  const bookingForOtherParty = useWatch({
    control,
    name: 'bookingForOtherParty',
  })

  const selectedPurposeId = useWatch({ control, name: 'purposeOfRequest' })

  const isDescriptionRequired = useMemo(() => {
    if (!selectedPurposeId) return false
    const selectedPurpose = bookingPurposeOptions.byId.get(selectedPurposeId)
    return selectedPurpose?.label.toLowerCase() === 'others'
  }, [selectedPurposeId, bookingPurposeOptions.byId])

  return (
    <Box>
      <Typography
        mb={2}
        mt={1}
        data-testid="StepOne-Title"
      >
        {ctIntl.formatMessage({
          id: 'carpool.list.addEditBooking.requestDetails',
        })}
      </Typography>
      <Stack
        gap={2}
        mb={2}
      >
        <Controller
          control={control}
          name="purposeOfRequest"
          render={({ field, fieldState }) => (
            <Autocomplete
              disabled={disabled}
              size="small"
              data-testid="StepOne-PurposeOfRequest"
              {...getAutocompleteVirtualizedProps({
                options: bookingPurposeOptions.array,
              })}
              onChange={(_, newValue) => {
                field.onChange(newValue ? newValue.id : null)
              }}
              value={
                field.value ? bookingPurposeOptions.byId.get(field.value) ?? null : null
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  required
                  label={ctIntl.formatMessage({
                    id: 'carpool.list.addEditBooking.purposeOfRequest',
                  })}
                  helperText={ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  })}
                  error={!!fieldState.error}
                />
              )}
            />
          )}
        />

        <TextFieldControlled
          disabled={disabled}
          size="small"
          ControllerProps={{ name: 'requestDescription', control }}
          label={ctIntl.formatMessage({
            id: 'carpool.list.addEditBooking.purposeRequestDescription',
          })}
          fullWidth
          variant="standard"
          required={isDescriptionRequired}
          data-testid="StepOne-RequestDescription"
        />

        {/* cannot be changed */}
        <TextFieldControlled
          disabled
          size="small"
          ControllerProps={{ name: 'requestor', control }}
          label={ctIntl.formatMessage({ id: 'carpool.list.addEditBooking.requestor' })}
          fullWidth
          variant="standard"
          data-testid="StepOne-Requestor"
        />

        <Controller
          control={control}
          name="bookingForOtherParty"
          render={({ field }) => (
            <FormControlLabel
              control={
                <Checkbox
                  checked={field.value}
                  onChange={(e) => field.onChange(e.target.checked)}
                  disabled={disabled}
                  data-testid="StepOne-BookingForOtherParty"
                />
              }
              label="Booking for other party"
            />
          )}
        />

        {bookingForOtherParty && (
          <UserAutocompleteRHF
            control={control}
            name="requestedForClientUserId"
            label="Requested for"
            disabled={disabled}
            required
            data-testid="StepOne-RequestedFor"
          />
        )}
      </Stack>
    </Box>
  )
}

export default StepOne
