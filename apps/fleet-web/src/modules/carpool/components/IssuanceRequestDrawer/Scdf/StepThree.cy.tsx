/// <reference types="@testing-library/cypress" />
import { zodResolver } from '@hookform/resolvers/zod'
import { Button, Stack } from '@karoo-ui/core'
import { useForm } from 'react-hook-form'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { getInputByTestId, mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import { EQUIPMENT_TYPE, JOURNEY_TYPE } from './constant'
import { generateBookingSchema, type BookingFormSchema } from './schema'
import StepThree from './StepThree'
import type { VehicleTypeAutocompleteOption } from './types'

// Test selectors using data-testid
const vehicleIdDataTestId = 'StepThree-VehicleId'
const vehicleTypeDataTestId = 'StepThree-VehicleType'
const numberOfPassengersDataTestId = 'StepThree-NumberOfPassengers'
const driverSelectionDataTestId = 'StepThree-DriverSelection'
const driverSelectDataTestId = 'StepThree-DriverSelect'
const vehicleCommanderTitleDataTestId = 'StepThree-VehicleCommanderTitle'
const vehicleCommanderSelectionDataTestId = 'StepThree-VehicleCommanderSelection'
const vehicleCommanderSelectDataTestId = 'StepThree-VehicleCommanderSelect'
const equipmentTypeDataTestId = 'StepThree-EquipmentType'
const equipmentSelectDataTestId = 'StepThree-EquipmentSelect'
const remarksInputDataTestId = 'RemarksInput'

// Mock data for testing
const mockVehicleTypeOptions = {
  array: [
    { id: 1, label: 'Sedan' },
    { id: 2, label: 'SUV' },
    { id: 3, label: 'Van' },
  ] as Array<VehicleTypeAutocompleteOption>,
  byId: new Map([
    [1, { id: 1, label: 'Sedan' }],
    [2, { id: 2, label: 'SUV' }],
    [3, { id: 3, label: 'Van' }],
  ]),
}

const defaultFormValues: BookingFormSchema = {
  purposeOfRequest: 1,
  requestDescription: '',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: null,
  vehicleId: null,
  numberOfPassengers: null,
  driverSelection: 'any' as const,
  driverId: null,
  vehicleCommanderSelection: 'any' as const,
  vehicleCommanderId: null,
  pickupTime: new Date(),
  pickupLocation: 1,
  dropoffTime: new Date(),
  journeyType: 'return' as const,
  journeys: [],
  equipmentType: 'no-equipment' as const,
  equipmentIds: [],
  uploadedImages: [],
  equipmentAttachmentIds: [],
  remarks: '',
}

// Test wrapper component
function TestStepThree({
  disabled = false,
  isKeyCollected = false,
  mode = 'create',
  vehicleIdExists = false,
  initialValues = defaultFormValues,
}: {
  disabled?: boolean
  isKeyCollected?: boolean
  mode?: 'create' | 'edit' | 'approve' | 'view'
  vehicleIdExists?: boolean
  initialValues?: Partial<BookingFormSchema>
}) {
  const schema = generateBookingSchema({
    carpoolBookingInAdvance: 24,
    carpoolBookingInAdvanceUnit: 'hours',
    carpoolMaximumBookingTime: 8,
    carpoolMaximumBookingTimeUnit: 'hours',
    carpoolAllowBackDateBooking: false,
    bookingPurposeByIdMap: new Map([[1, { id: 1, label: 'Official Business' }]]),
    mode,
  })

  const {
    control,
    setValue: setFormValue,
    trigger,
  } = useForm<BookingFormSchema>({
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: { ...defaultFormValues, ...initialValues },
  })

  const handleNext = async () =>
    await trigger([
      'driverSelection',
      'driverId',
      'vehicleTypeId',
      'vehicleId',
      'numberOfPassengers',
      'vehicleCommanderSelection',
      'vehicleCommanderId',
      'equipmentIds',
    ])

  return (
    <Stack>
      <StepThree
        control={control}
        setFormValue={setFormValue}
        vehicleTypeOptions={mockVehicleTypeOptions}
        disabled={disabled}
        isKeyCollected={isKeyCollected}
        mode={mode}
        vehicleIdExists={vehicleIdExists}
      />
      <Button
        onClick={handleNext}
        data-testid="StepThree-NextButton"
      >
        Next
      </Button>
    </Stack>
  )
}

const mountStepThree = (props?: Parameters<typeof TestStepThree>[0]) => {
  // Mock API calls
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    if (req.body.method === 'ct_fleet_get_booking_options') {
      req.reply({
        body: {
          id: 10,
          result: {
            equipments: [
              { id: 1, name: 'Laptop' },
              { id: 2, name: 'Projector' },
            ],
            drivers: [
              { id: 1, name: 'John Doe' },
              { id: 2, name: 'Jane Smith' },
            ],
          },
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_available_vehicles') {
      req.reply({
        body: {
          id: 10,
          result: [
            { id: 1, registration: 'ABC123', vehicleTypeId: 1 },
            { id: 2, registration: 'DEF456', vehicleTypeId: 2 },
          ],
        },
      })
    }
  }).as('getBookingOptions')

  mountWithProviders(<TestStepThree {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user().mockState,
      },
    },
  })
}

describe('StepThree Component', () => {
  describe('Basic Rendering', () => {
    it('should render all form sections correctly', () => {
      mountStepThree()

      // Check vehicle section
      cy.findByTestId(vehicleTypeDataTestId).should('be.visible')
      cy.findByTestId(vehicleIdDataTestId).should('not.exist')
      cy.findByTestId(numberOfPassengersDataTestId).should('be.visible')

      // Check driver section
      cy.findByTestId(driverSelectionDataTestId + '-Driver').should('be.visible')
      cy.findByTestId(driverSelectionDataTestId + '-Any').should('be.visible')
      cy.findByTestId(driverSelectionDataTestId + '-Self-drive').should('be.visible')
      cy.findByTestId(driverSelectionDataTestId + '-Specific').should('be.visible')

      // Check vehicle commander section
      cy.findByTestId(vehicleCommanderTitleDataTestId).should('be.visible')

      // Check equipment section
      cy.findByTestId('StepThree-EquipmentsTitle').should('be.visible')
      cy.contains('No equipment').should('be.visible')

      // Check remarks section
      cy.findByTestId('StepThree-RemarksTitle').should('be.visible')
      cy.findByTestId(remarksInputDataTestId).should('be.visible')

      // Driver selection should default to "Any"
      cy.findByTestId(driverSelectionDataTestId + '-any').should('be.checked')

      // Initially, driver autocomplete should not be visible
      cy.findByTestId(driverSelectDataTestId).should('not.exist')

      // Vehicle commander selection should default to "Any"
      cy.findByTestId(vehicleCommanderSelectionDataTestId + '-any').should('be.checked')

      // Equipment type should default to "No equipment"
      cy.findByTestId(equipmentTypeDataTestId + '-none').should('be.checked')
    })

    it('should show validation error ', () => {
      mountStepThree()

      // trigger the form
      cy.findByTestId('StepThree-NextButton').click()

      // Check for validation error
      cy.findByTestId(vehicleTypeDataTestId)
        .find('.MuiFormHelperText-root')
        .should('contain.text', messages.required)

      cy.findByTestId(numberOfPassengersDataTestId)
        .find('.MuiFormHelperText-root')
        .should('contain.text', messages.required)
    })
  })

  describe('Vehicle Type Selection', () => {
    it('should allow selecting a vehicle type from dropdown', () => {
      mountStepThree()

      // Open dropdown
      cy.findByTestId(vehicleTypeDataTestId).click()

      // Check all options are visible
      cy.contains(mockVehicleTypeOptions.array[0].label).should('be.visible')
      cy.contains(mockVehicleTypeOptions.array[1].label).should('be.visible')
      cy.contains(mockVehicleTypeOptions.array[2].label).should('be.visible')

      // Select an option
      cy.contains(mockVehicleTypeOptions.array[0].label).click()

      // Verify selection
      cy.findByTestId(vehicleTypeDataTestId).should('contain.text', 'SUV')
    })

    it('should disable vehicle type when key is collected', () => {
      mountStepThree({ isKeyCollected: true })

      getInputByTestId(vehicleTypeDataTestId).should('be.disabled')
    })
  })

  describe('Vehicle ID Selection (when vehicleIdExists)', () => {
    it('should show vehicle registration field when vehicleIdExists is true', () => {
      mountStepThree({ vehicleIdExists: true })

      cy.findByTestId(vehicleIdDataTestId).should('exist')
    })
  })

  describe('Driver Selection', () => {
    it('should allow selecting different driver options', () => {
      mountStepThree()

      // Test self-drive selection
      cy.findByTestId(driverSelectionDataTestId + '-self').click()
      cy.findByTestId(driverSelectionDataTestId + '-self').should('be.checked')
      cy.findByTestId(driverSelectionDataTestId + '-any').should('not.be.checked')

      // Test specific driver selection
      cy.findByTestId(driverSelectionDataTestId + '-specific').click()
      cy.findByTestId(driverSelectionDataTestId + '-specific').should('be.checked')
      cy.findByTestId(driverSelectionDataTestId + '-self').should('not.be.checked')

      // Driver autocomplete should now be visible
      cy.findByTestId(driverSelectDataTestId).should('be.visible')
    })

    it('should hide driver selection in approve mode', () => {
      mountStepThree({ mode: 'approve' })

      // Driver selection radio buttons should not be visible
      cy.findByTestId(driverSelectionDataTestId + '-Any').should('not.exist')
      cy.findByTestId(driverSelectionDataTestId + '-Self-drive').should('not.exist')
      cy.findByTestId(driverSelectionDataTestId + '-Specific').should('not.exist')
    })
  })

  describe('Vehicle Commander Selection', () => {
    it('should show info alert for single and return journey types', () => {
      mountStepThree({
        initialValues: {
          journeyType: JOURNEY_TYPE.SINGLE,
        },
      })

      cy.findByTestId('StepThree-VehicleCommanderAlert').should('exist')
    })

    it('should allow selecting different vehicle commander options', () => {
      mountStepThree()

      // Test self-command selection
      cy.findByTestId('StepThree-VehicleCommanderSelection-self').click()
      cy.findByTestId('StepThree-VehicleCommanderSelection-self').should('be.checked')

      // Test specific commander selection
      cy.findByTestId('StepThree-VehicleCommanderSelection-specific').click()
      cy.findByTestId('StepThree-VehicleCommanderSelection-specific').should(
        'be.checked',
      )
    })

    it('should show user autocomplete when "Specific" is selected', () => {
      mountStepThree()

      // Initially, user autocomplete should not be visible
      cy.findByTestId(vehicleCommanderSelectDataTestId).should('not.exist')

      // Select "Specific"
      cy.findByTestId('StepThree-VehicleCommanderSelection-specific').click()

      // User autocomplete should now be visible
      cy.findByTestId(vehicleCommanderSelectDataTestId).should('be.visible')
    })
  })

  describe('Equipment Selection', () => {
    it('should allow selecting different equipment types', () => {
      mountStepThree()

      // Test car boot selection
      cy.findByTestId('StepThree-EquipmentType-car-boot').click()
      cy.findByTestId('StepThree-EquipmentType-car-boot').should('be.checked')
      cy.findByTestId('StepThree-EquipmentType-none').should('not.be.checked')

      // Test larger space selection
      cy.findByTestId('StepThree-EquipmentType-larger-space').click()
      cy.findByTestId('StepThree-EquipmentType-larger-space').should('be.checked')
      cy.findByTestId('StepThree-EquipmentType-car-boot').should('not.be.checked')
    })

    it('should show equipment autocomplete when equipment type is not "No equipment"', () => {
      mountStepThree()

      // Initially, equipment autocomplete should not be visible
      cy.findByLabelText(/Equipments/i).should('not.exist')

      // Select equipment type
      cy.findByLabelText('Can fit within a car boot').click()

      // Equipment autocomplete should now be visible
      cy.findByLabelText(/Equipments/i).should('be.visible')
    })

    it('should show image uploader when equipment type is not "No equipment"', () => {
      mountStepThree()

      // Select equipment type
      cy.findByLabelText('Can fit within a car boot').click()

      // Image uploader should be visible
      cy.get('[data-testid*="image-upload"]').should('exist')
    })

    it('should clear equipment data when switching to "No equipment"', () => {
      mountStepThree({
        initialValues: {
          equipmentType: EQUIPMENT_TYPE.CAR_BOOT,
          equipmentIds: [1],
        },
      })

      // Switch to "No equipment"
      cy.findByLabelText('No equipment').click()

      // Equipment fields should be hidden
      cy.findByLabelText(/Equipments/i).should('not.exist')
    })
  })

  describe('Remarks Field', () => {
    it('should allow typing in remarks field', () => {
      mountStepThree()

      const testRemarks = 'This is a test remark for the booking'
      getInputByTestId(remarksInputDataTestId).type(testRemarks)
      getInputByTestId(remarksInputDataTestId).should('have.value', testRemarks)
    })
  })

  describe('Disabled State', () => {
    it('should disable all form fields when disabled prop is true', () => {
      mountStepThree({ disabled: true })

      // All interactive fields should be disabled
      cy.findByLabelText(/Vehicle type/i).should('be.disabled')
      cy.findByLabelText(/No of passengers/i).should('be.disabled')
      cy.findByLabelText('Any').first().should('be.disabled')
      cy.findByLabelText('Self-drive').should('be.disabled')
      cy.findByLabelText('Specific').should('be.disabled')
      cy.findByTestId(remarksInputDataTestId).find('input').should('be.disabled')
    })
  })

  describe('Form Integration', () => {
    it('should maintain form state correctly across interactions', () => {
      mountStepThree()

      // Fill out the form
      cy.findByLabelText(/Vehicle type/i).click()
      cy.contains('SUV').click()

      cy.findByLabelText(/No of passengers/i).type('3')

      cy.findByLabelText('Self-drive').click()

      cy.findByLabelText('Can fit within a car boot').click()

      cy.findByTestId(remarksInputDataTestId).find('input').type('Test remarks')

      // Verify all values are maintained
      cy.findByLabelText(/Vehicle type/i).should('contain.text', 'SUV')
      cy.findByLabelText(/No of passengers/i).should('have.value', '3')
      cy.findByLabelText('Self-drive').should('be.checked')
      cy.findByLabelText('Can fit within a car boot').should('be.checked')
      cy.findByTestId(remarksInputDataTestId)
        .find('input')
        .should('have.value', 'Test remarks')

      // Trigger validation
      cy.findByTestId('StepThree-NextButton').click()

      // No validation errors should be present
      cy.findByTestId(vehicleTypeDataTestId)
        .find('.MuiFormHelperText-root')
        .should('not.exist')
      cy.findByTestId(numberOfPassengersDataTestId)
        .find('.MuiFormHelperText-root')
        .should('not.exist')
    })
  })
})
