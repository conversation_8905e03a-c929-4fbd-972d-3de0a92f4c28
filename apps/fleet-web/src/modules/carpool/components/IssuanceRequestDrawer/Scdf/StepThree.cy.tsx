/// <reference types="@testing-library/cypress" />
import { zodResolver } from '@hookform/resolvers/zod'
import { Button, Stack } from '@karoo-ui/core'
import { useForm } from 'react-hook-form'
import { match } from 'ts-pattern'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import {
  carpoolEndpointMocks,
  restApiMocks,
} from 'src/cypress-ct/mocks/endpoints/carpool'
import { getInputByTestId, mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import { EQUIPMENT_TYPE, JOURNEY_TYPE } from './constant'
import { generateBookingSchema, type BookingFormSchema } from './schema'
import StepThree from './StepThree'
import type { VehicleTypeAutocompleteOption } from './types'

// Test selectors using data-testid
const vehicleIdDataTestId = 'StepThree-VehicleId'
const vehicleTypeDataTestId = 'StepThree-VehicleType'
const numberOfPassengersDataTestId = 'StepThree-NumberOfPassengers'
const driverSelectionDataTestId = 'StepThree-DriverSelection'
const driverSelectDataTestId = 'StepThree-DriverSelect'
const vehicleCommanderTitleDataTestId = 'StepThree-VehicleCommanderTitle'
const vehicleCommanderSelectionDataTestId = 'StepThree-VehicleCommanderSelection'
const vehicleCommanderSelectDataTestId = 'StepThree-VehicleCommanderSelect'
const equipmentTypeDataTestId = 'StepThree-EquipmentType'

const remarksInputDataTestId = 'RemarksInput'

// Mock data for testing
const mockVehicleTypeOptions = {
  array: [
    { id: 1, label: 'Sedan' },
    { id: 2, label: 'SUV' },
    { id: 3, label: 'Van' },
  ] as Array<VehicleTypeAutocompleteOption>,
  byId: new Map([
    [1, { id: 1, label: 'Sedan' }],
    [2, { id: 2, label: 'SUV' }],
    [3, { id: 3, label: 'Van' }],
  ]),
}

const defaultFormValues: BookingFormSchema = {
  purposeOfRequest: 1,
  requestDescription: '',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: null,
  vehicleId: null,
  numberOfPassengers: null,
  driverSelection: 'any' as const,
  driverId: null,
  vehicleCommanderSelection: 'any' as const,
  vehicleCommanderId: null,
  pickupTime: new Date(),
  pickupLocation: 1,
  dropoffTime: new Date(),
  journeyType: 'return' as const,
  journeys: [],
  equipmentType: 'no-equipment' as const,
  equipmentIds: [],
  uploadedImages: [],
  equipmentAttachmentIds: [],
  remarks: '',
}

// Test wrapper component
function TestStepThree({
  disabled = false,
  isKeyCollected = false,
  mode = 'create',
  vehicleIdExists = false,
  initialValues = defaultFormValues,
}: {
  disabled?: boolean
  isKeyCollected?: boolean
  mode?: 'create' | 'edit' | 'approve' | 'view'
  vehicleIdExists?: boolean
  initialValues?: Partial<BookingFormSchema>
}) {
  const schema = generateBookingSchema({
    carpoolBookingInAdvance: 24,
    carpoolBookingInAdvanceUnit: 'hours',
    carpoolMaximumBookingTime: 8,
    carpoolMaximumBookingTimeUnit: 'hours',
    carpoolAllowBackDateBooking: false,
    bookingPurposeByIdMap: new Map([[1, { id: 1, label: 'Official Business' }]]),
    mode,
  })

  const {
    control,
    setValue: setFormValue,
    trigger,
  } = useForm<BookingFormSchema>({
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: { ...defaultFormValues, ...initialValues },
  })

  const handleNext = async () =>
    await trigger([
      'driverSelection',
      'driverId',
      'vehicleTypeId',
      'vehicleId',
      'numberOfPassengers',
      'vehicleCommanderSelection',
      'vehicleCommanderId',
      'equipmentIds',
    ])

  return (
    <Stack>
      <StepThree
        control={control}
        setFormValue={setFormValue}
        vehicleTypeOptions={mockVehicleTypeOptions}
        disabled={disabled}
        isKeyCollected={isKeyCollected}
        mode={mode}
        vehicleIdExists={vehicleIdExists}
      />
      <Button
        onClick={handleNext}
        data-testid="StepThree-NextButton"
      >
        Next
      </Button>
    </Stack>
  )
}

const mountStepThree = (props?: Parameters<typeof TestStepThree>[0]) => {
  // Mock JSON-RPC API calls
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    match(req.body.method)
      .with('ct_fleet_get_booking_options', () =>
        req.reply(carpoolEndpointMocks.ct_fleet_get_booking_options()),
      )
      .with('ct_fleet_carpool_get_booking_options', () =>
        req.reply(carpoolEndpointMocks.ct_fleet_carpool_get_booking_options()),
      )
      .with('ct_fleet_get_available_vehicles', () =>
        req.reply(carpoolEndpointMocks.ct_fleet_get_available_vehicles()),
      )
      .otherwise(() => {})
  })

  // Mock REST API calls
  cy.intercept('GET', '/scdf/accessory', () => restApiMocks['GET /scdf/accessory']())

  cy.intercept('GET', '/scdf/vehicle/vehicle-for-driver*', (req) => {
    const url = new URL(req.url)
    const clientDriverId = url.searchParams.get('clientDriverId')
    return restApiMocks['GET /scdf/vehicle/vehicle-for-driver']({
      clientDriverId: clientDriverId || undefined,
    })
  })

  mountWithProviders(<TestStepThree {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user().mockState,
      },
    },
  })
}

describe('StepThree Component', () => {
  it('should fill and validate form', () => {
    mountStepThree()

    // trigger the form to check initial validation errors
    cy.findByTestId('StepThree-NextButton').click()

    // Check for validation errors for required fields
    cy.findByTestId(vehicleTypeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('contain.text', messages.required)

    cy.findByTestId(numberOfPassengersDataTestId)
      .find('.MuiFormHelperText-root')
      .should('contain.text', messages.required)

    /**************************************** Vehicle Section ****************************************/

    // Check vehicle section is visible
    cy.findByTestId(vehicleTypeDataTestId).should('be.visible')
    cy.findByTestId(vehicleIdDataTestId).should('not.exist')
    cy.findByTestId(numberOfPassengersDataTestId).should('be.visible')

    // Select vehicle type
    cy.findByTestId(vehicleTypeDataTestId).click()

    // Check all options are visible
    cy.contains(mockVehicleTypeOptions.array[0].label).should('be.visible')
    cy.contains(mockVehicleTypeOptions.array[1].label).should('be.visible')
    cy.contains(mockVehicleTypeOptions.array[2].label).should('be.visible')

    // Select SUV
    cy.contains(mockVehicleTypeOptions.array[1].label).click()

    // Verify selection
    cy.findByTestId(vehicleTypeDataTestId).should(
      'contain.text',
      mockVehicleTypeOptions.array[1].label,
    )

    // Fill number of passengers
    getInputByTestId(numberOfPassengersDataTestId).type('3')

    /**************************************** Driver Section ****************************************/

    // Check driver section
    cy.findByTestId(driverSelectionDataTestId + '-Driver').should('be.visible')
    cy.findByTestId(driverSelectionDataTestId + '-Any').should('be.visible')
    cy.findByTestId(driverSelectionDataTestId + '-Self-drive').should('be.visible')
    cy.findByTestId(driverSelectionDataTestId + '-Specific').should('be.visible')

    // Driver selection should default to "Any"
    cy.findByTestId(driverSelectionDataTestId + '-any').should('be.checked')

    // Initially, driver autocomplete should not be visible
    cy.findByTestId(driverSelectDataTestId).should('not.exist')

    // Test self-drive selection
    cy.findByTestId(driverSelectionDataTestId + '-self').click()
    cy.findByTestId(driverSelectionDataTestId + '-self').should('be.checked')
    cy.findByTestId(driverSelectionDataTestId + '-any').should('not.be.checked')

    // Test specific driver selection
    cy.findByTestId(driverSelectionDataTestId + '-specific').click()
    cy.findByTestId(driverSelectionDataTestId + '-specific').should('be.checked')
    cy.findByTestId(driverSelectionDataTestId + '-self').should('not.be.checked')

    // Driver autocomplete should now be visible
    cy.findByTestId(driverSelectDataTestId).should('be.visible')

    // Switch back to "Any" for simplicity
    cy.findByTestId(driverSelectionDataTestId + '-any').click()
    cy.findByTestId(driverSelectionDataTestId + '-any').should('be.checked')
    cy.findByTestId(driverSelectDataTestId).should('not.exist')

    /**************************************** Vehicle Commander Section ****************************************/

    // Check vehicle commander section
    cy.findByTestId(vehicleCommanderTitleDataTestId).should('be.visible')

    // Vehicle commander selection should default to "Any"
    cy.findByTestId(vehicleCommanderSelectionDataTestId + '-any').should('be.checked')

    // Initially, user autocomplete should not be visible
    cy.findByTestId(vehicleCommanderSelectDataTestId).should('not.exist')

    // Test self-command selection
    cy.findByTestId('StepThree-VehicleCommanderSelection-self').click()
    cy.findByTestId('StepThree-VehicleCommanderSelection-self').should('be.checked')

    // Test specific commander selection
    cy.findByTestId('StepThree-VehicleCommanderSelection-specific').click()
    cy.findByTestId('StepThree-VehicleCommanderSelection-specific').should('be.checked')

    // User autocomplete should now be visible
    cy.findByTestId(vehicleCommanderSelectDataTestId).should('be.visible')

    // Switch back to "Any" for simplicity
    cy.findByTestId(vehicleCommanderSelectionDataTestId + '-any').click()
    cy.findByTestId(vehicleCommanderSelectionDataTestId + '-any').should('be.checked')
    cy.findByTestId(vehicleCommanderSelectDataTestId).should('not.exist')

    /**************************************** Equipment Section ****************************************/

    // Check equipment section
    cy.findByTestId('StepThree-EquipmentsTitle').should('be.visible')
    cy.contains('No equipment').should('be.visible')

    // Equipment type should default to "No equipment"
    cy.findByTestId(equipmentTypeDataTestId + '-none').should('be.checked')

    // Initially, equipment autocomplete should not be visible
    cy.findByLabelText(/Equipments/i).should('not.exist')

    // Test car boot selection
    cy.findByTestId('StepThree-EquipmentType-car-boot').click()
    cy.findByTestId('StepThree-EquipmentType-car-boot').should('be.checked')
    cy.findByTestId('StepThree-EquipmentType-none').should('not.be.checked')

    // Equipment autocomplete should now be visible
    cy.findByLabelText(/Equipments/i).should('be.visible')

    // Image uploader should be visible
    cy.get('[data-testid*="image-upload"]').should('exist')

    // Test larger space selection
    cy.findByTestId('StepThree-EquipmentType-larger-space').click()
    cy.findByTestId('StepThree-EquipmentType-larger-space').should('be.checked')
    cy.findByTestId('StepThree-EquipmentType-car-boot').should('not.be.checked')

    // Switch back to "No equipment" for simplicity
    cy.findByTestId(equipmentTypeDataTestId + '-none').click()
    cy.findByTestId(equipmentTypeDataTestId + '-none').should('be.checked')

    // Equipment fields should be hidden
    cy.findByLabelText(/Equipments/i).should('not.exist')

    /**************************************** Remarks Section ****************************************/

    // Check remarks section
    cy.findByTestId('StepThree-RemarksTitle').should('be.visible')
    cy.findByTestId(remarksInputDataTestId).should('be.visible')

    // Fill remarks
    const testRemarks = 'This is a test remark for the booking'
    getInputByTestId(remarksInputDataTestId).type(testRemarks)
    getInputByTestId(remarksInputDataTestId).should('have.value', testRemarks)

    /**************************************** Final Validation ****************************************/

    // Trigger validation - should pass now
    cy.findByTestId('StepThree-NextButton').click()

    // No validation errors should be present
    cy.findByTestId(vehicleTypeDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')

    cy.findByTestId(numberOfPassengersDataTestId)
      .find('.MuiFormHelperText-root')
      .should('not.exist')
  })

  it('should disable vehicle type when key is collected', () => {
    mountStepThree({ isKeyCollected: true })

    getInputByTestId(vehicleTypeDataTestId).should('be.disabled')
  })

  it('should show vehicle registration field when vehicleIdExists is true', () => {
    mountStepThree({ vehicleIdExists: true })

    cy.findByTestId(vehicleIdDataTestId).should('exist')
  })

  it('should hide driver selection in approve mode', () => {
    mountStepThree({ mode: 'approve' })

    // Driver selection radio buttons should not be visible
    cy.findByTestId(driverSelectionDataTestId + '-Any').should('not.exist')
    cy.findByTestId(driverSelectionDataTestId + '-Self-drive').should('not.exist')
    cy.findByTestId(driverSelectionDataTestId + '-Specific').should('not.exist')
  })

  it('should show info alert for single and return journey types', () => {
    mountStepThree({
      initialValues: {
        journeyType: JOURNEY_TYPE.SINGLE,
      },
    })

    cy.findByTestId('StepThree-VehicleCommanderAlert').should('exist')
  })

  it('should clear equipment data when switching to "No equipment"', () => {
    mountStepThree({
      initialValues: {
        equipmentType: EQUIPMENT_TYPE.CAR_BOOT,
        equipmentIds: [1 as any], // Type assertion for test purposes
      },
    })

    // Switch to "No equipment"
    cy.findByLabelText('No equipment').click()

    // Equipment fields should be hidden
    cy.findByLabelText(/Equipments/i).should('not.exist')
  })

  it('should disable all form fields when disabled prop is true', () => {
    mountStepThree({ disabled: true })

    // All interactive fields should be disabled
    cy.findByLabelText(/Vehicle type/i).should('be.disabled')
    cy.findByLabelText(/No of passengers/i).should('be.disabled')
    cy.findByLabelText('Any').first().should('be.disabled')
    cy.findByLabelText('Self-drive').should('be.disabled')
    cy.findByLabelText('Specific').should('be.disabled')
    cy.findByTestId(remarksInputDataTestId).find('input').should('be.disabled')
  })
})
