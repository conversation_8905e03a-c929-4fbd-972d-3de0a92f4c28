import type { ReactNode } from 'react'
import { Box, IconButton, Typography } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'

import type { CarpoolBookingId } from 'api/types'

type BookingDrawerLayoutProps = {
  onClose: () => void
  title: string
  bookingId: CarpoolBookingId | null
  children: ReactNode
  actionButtons: ReactNode
  headerContent?: ReactNode
}

export default function BookingDrawerLayout({
  onClose,
  title,
  bookingId,
  headerContent,
  children,
  actionButtons,
}: BookingDrawerLayoutProps) {
  return (
    <Box
      sx={{
        display: 'grid',
        width: '520px',
        height: '100vh',
        gridTemplateRows: 'min-content 1fr min-content',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          px: 3,
          pt: 2,
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Typography
              variant="h5"
              data-testid="BookingDrawerLayout-Title"
            >
              {title}
            </Typography>
            {bookingId && (
              <Typography
                sx={{ opacity: 0.6 }}
                variant="h5"
              >
                {bookingId}
              </Typography>
            )}
          </Box>
          <IconButton
            data-testid="BookingDrawerLayout-CloseButton"
            onClick={onClose}
            edge="end"
            size="medium"
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Additional header content (e.g., Stepper) */}
        {headerContent}
      </Box>

      {/* Main content */}
      <Box
        sx={{
          px: 3,
          py: 2,
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        {children}
      </Box>

      {/* Action buttons */}
      <Box
        sx={{
          px: 3,
          borderTop: '1px solid',
          borderColor: 'divider',
          mt: 'auto',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', my: 1.5 }}>
          {actionButtons}
        </Box>
      </Box>
    </Box>
  )
}
