import { useEffect, useMemo, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Stepper } from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { useForm, useWatch } from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type { CarpoolBookingId } from 'api/types'
import { getUser } from 'duxs/user'
import {
  getCarpoolAllowBackDateBooking,
  getSettings_UNSAFE,
} from 'duxs/user-sensitive-selectors'
import { useUsersQuery } from 'src/modules/api/useUsersQuery'
import { useTypedSelector } from 'src/redux-hooks'

import { ctIntl } from 'cartrack-ui-kit'
import useVehiclesAndDriversOptions from '../../../hooks/useVehiclesAndDriversOptions'
import { useCarpoolBookingTimeRules } from '../../../Settings/Rules/api/queries'
import {
  BookingStatus as BookingStatusEnum,
  type BookingStatus,
} from '../../../utils/constants'
import type useBookingAttachmentsQuery from './api/useBookingAttachmentsQuery'
import BookingDrawerLayout from './components/BookingDrawerLayout'
import { useBookingOptions } from './hooks/useBookingOptions'
import { useVehicleTypeOptions } from './hooks/useVehicleTypeOptions'
import {
  generateBookingSchema,
  type BookingFormSchema,
  type ValidSchema,
} from './schema'
import StepOne from './StepOne'
import StepThree from './StepThree'
import StepTwo from './StepTwo'
import { STEPS } from './types'

type BaseProps = {
  onClose: () => void
  onSubmit: (values: ValidSchema) => void
  onSubmitLoading: boolean
}

type CreateProps = BaseProps & {
  mode: 'create'
}

type DuplicateProps = BaseProps & {
  mode: 'duplicate'
  initialValues: BookingFormSchema
  attachmentsQuery: ReturnType<typeof useBookingAttachmentsQuery>
}

type EditProps = BaseProps & {
  mode: 'edit'
  initialValues: BookingFormSchema
  bookingId: CarpoolBookingId
  bookingStatusId: BookingStatus
  isKeyCollected: boolean
  attachmentsQuery: ReturnType<typeof useBookingAttachmentsQuery>
}

type Props = CreateProps | DuplicateProps | EditProps

type BookingStep = 0 | 1 | 2

export default function CreateEditBookingDrawer(props: Props) {
  const { onClose, onSubmit, onSubmitLoading, mode } = props
  const [currentStep, setCurrentStep] = useState<BookingStep>(0)

  // Hooks that are always needed
  const user = useTypedSelector(getUser)
  const carpoolAllowBackDateBooking = useTypedSelector(getCarpoolAllowBackDateBooking)
  const {
    carpoolBookingInAdvance,
    carpoolBookingInAdvanceUnit,
    carpoolMaximumBookingTime,
    carpoolMaximumBookingTimeUnit,
  } = useCarpoolBookingTimeRules()

  // Conditional hooks for edit mode
  const { carpoolEditBookings } = useTypedSelector(getSettings_UNSAFE)

  // Preload the user query data to use
  // FIXME: use this query to get the users
  useUsersQuery()

  // Get mode-specific props
  const editProps = mode === 'edit' ? props : null
  const bookingId = editProps?.bookingId
  const bookingStatusId = editProps?.bookingStatusId
  const isKeyCollected = editProps?.isKeyCollected ?? false
  const attachmentsQuery = mode !== 'create' ? props?.attachmentsQuery : undefined

  // Get all booking options using the custom hook (without selectedPurposeOfRequest for now)
  const { locationOptions, bookingPurposeOptions, carpoolOptionsData } =
    useBookingOptions()
  const { driverOptions } = useVehiclesAndDriversOptions(carpoolOptionsData)

  const validSchema = useMemo(
    () =>
      generateBookingSchema({
        carpoolBookingInAdvance,
        carpoolBookingInAdvanceUnit,
        carpoolMaximumBookingTime,
        carpoolMaximumBookingTimeUnit,
        carpoolAllowBackDateBooking,
        bookingPurposeByIdMap: bookingPurposeOptions.byId,
        mode,
        statusId: bookingStatusId,
      }),
    [
      carpoolBookingInAdvance,
      carpoolBookingInAdvanceUnit,
      carpoolMaximumBookingTime,
      carpoolMaximumBookingTimeUnit,
      carpoolAllowBackDateBooking,
      bookingPurposeOptions.byId,
      mode,
      bookingStatusId,
    ],
  )

  const initialValues = useMemo(
    (): BookingFormSchema =>
      match(props)
        .with({ mode: 'create' }, () => ({
          driverId: driverOptions.array.length === 1 ? driverOptions.array[0].id : null,
          purposeOfRequest:
            bookingPurposeOptions.array.length === 1
              ? bookingPurposeOptions.array[0].id
              : null,
          requestDescription: '',
          requestor: user?.username || '',
          bookingForOtherParty: false,
          requestedForClientUserId: null,
          vehicleTypeId: null,
          vehicleId: null,
          numberOfPassengers: null,
          driverSelection: 'any' as const,
          vehicleCommanderSelection: 'any' as const,
          vehicleCommanderId: null,
          pickupTime: null,
          pickupLocation:
            locationOptions.array.length === 1 ? locationOptions.array[0].id : null,
          dropoffTime: null,
          journeyType: 'return' as const,
          journeys: [],
          equipmentType: 'no-equipment' as const,
          equipmentIds: [],
          uploadedImages: [],
          equipmentAttachmentIds: [],
          remarks: '',
        }))
        .with({ mode: 'duplicate' }, ({ initialValues }) => ({
          ...initialValues,
          pickupTime: null,
          dropoffTime: null,
          vehicleCommanderSelection: 'any' as const,
          vehicleCommanderId: null,
          vehicleId: null,
          driverId: null,
          driverSelection: 'any' as const,
        }))
        .with(
          { mode: 'edit' },
          ({ initialValues }) => initialValues as BookingFormSchema,
        )
        .exhaustive(),

    [
      props,
      driverOptions.array,
      bookingPurposeOptions.array,
      user?.username,
      locationOptions.array,
    ],
  )

  // Setup form with the complete schema for all steps
  const {
    control,
    handleSubmit,
    setValue: setFormValue,
    trigger,
    formState: { isValid: isFormValid, isDirty },
  } = useForm<BookingFormSchema>({
    resolver: zodResolver(validSchema),
    mode: 'onChange',
    defaultValues: initialValues,
  })

  // NOTE: when vehicle id exists already, override the vehicle type selection
  // https://gitlab.cartrack.com/cartrack-base/cartrack-special-projects/cartrack-tfms-dev/tfms-web/-/issues/84
  const vehicleIdExists = mode === 'edit' && initialValues.vehicleId !== null

  // Get form state for dynamic options
  const selectedPurposeOfRequest = useWatch({ name: 'purposeOfRequest', control })

  // Get vehicle type options that depend on selected purpose
  const vehicleTypeOptions = useVehicleTypeOptions(selectedPurposeOfRequest)

  // Handle next step navigation with validation
  const handleNext = async () => {
    const isValid = await match(currentStep)
      .with(
        0,
        async () =>
          await trigger([
            'purposeOfRequest',
            'requestDescription',
            'requestor',
            'bookingForOtherParty',
            'requestedForClientUserId',
          ]),
      )
      .with(
        1,
        async () =>
          await trigger([
            'pickupTime',
            'dropoffTime',
            'pickupLocation',
            'journeyType',
            'journeys',
          ]),
      )
      .with(
        2,
        async () =>
          await trigger([
            'driverSelection',
            'driverId',
            'vehicleTypeId',
            'vehicleId',
            'numberOfPassengers',
            'vehicleCommanderSelection',
            'vehicleCommanderId',
            'equipmentIds',
          ]),
      )
      .exhaustive()

    if (isValid) {
      setCurrentStep((prev) => Math.min(prev + 1, 2) as BookingStep)
    }
  }

  // Handle previous step navigation
  const handleBack = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0) as BookingStep)
  }

  // Calculate disabled state for edit mode
  const disabled = useMemo(
    () =>
      R.isNonNullish(bookingStatusId) &&
      bookingStatusId !== BookingStatusEnum.BOOKING_STATUS_REQUESTED &&
      (bookingStatusId === BookingStatusEnum.BOOKING_STATUS_FREE ||
        // NOTE: only cancelled bookings should be disabled
        bookingStatusId === BookingStatusEnum.BOOKING_STATUS_CANCELLED ||
        !carpoolEditBookings),
    [bookingStatusId, carpoolEditBookings],
  )

  const submitForm = handleSubmit((values) => {
    const formValues = values as ValidSchema
    onSubmit(formValues)
  })

  const maxDateTimeInAdvance =
    carpoolBookingInAdvance > 0
      ? DateTime.now().plus({
          [carpoolBookingInAdvanceUnit]: carpoolBookingInAdvance,
        })
      : undefined

  // Auto-select single options for dynamic selections that depend on form state (create mode only)
  useEffect(() => {
    if (mode === 'create' && vehicleTypeOptions.array.length === 1) {
      setFormValue('vehicleTypeId', vehicleTypeOptions.array[0].id, {
        shouldValidate: true,
      })
    }
  }, [mode, vehicleTypeOptions.array, setFormValue])

  const getHeaderTitle = () => {
    if (mode === 'create' || mode === 'duplicate') {
      return ctIntl.formatMessage({ id: 'tfms.list.createBooking.header' })
    } else {
      return ctIntl.formatMessage({
        id: disabled
          ? 'carpool.bookingDetails.booking'
          : 'carpool.list.addEditBooking.header',
      })
    }
  }

  return (
    <BookingDrawerLayout
      onClose={onClose}
      title={getHeaderTitle()}
      bookingId={mode === 'edit' && bookingId ? bookingId : null}
      headerContent={
        <Stepper
          activeStep={currentStep}
          sx={{ mb: 4, mt: 2 }}
        >
          {STEPS.map((step) => (
            <Step
              key={step.id}
              data-testid={`CreateEditBookingDrawer-Step-${step.id}`}
            >
              <StepLabel
                data-testid={`CreateEditBookingDrawer-StepLabel-${step.id}`}
                sx={{
                  flexDirection: 'column',
                  '& .MuiStepLabel-labelContainer': {
                    marginTop: 1,
                    textAlign: 'center',
                  },
                  '& .MuiStepLabel-iconContainer': {
                    '.Mui-completed': {
                      color: 'success.main',
                    },
                  },
                }}
              >
                {step.label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      }
      actionButtons={match(currentStep)
        // First step - Cancel and Next
        .with(0, () => (
          <>
            <Button
              onClick={onClose}
              variant="outlined"
              color="secondary"
              data-testid="CreateEditBookingDrawer-Cancel"
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              onClick={handleNext}
              variant="contained"
              color="primary"
              data-testid="CreateEditBookingDrawer-Next"
            >
              {ctIntl.formatMessage({ id: 'Next' })}
            </Button>
          </>
        ))
        // Middle step - Back and Next
        .with(1, () => (
          <>
            <Button
              onClick={handleBack}
              variant="outlined"
              color="secondary"
              data-testid="CreateEditBookingDrawer-Back"
            >
              {ctIntl.formatMessage({ id: 'Back' })}
            </Button>
            <Button
              onClick={handleNext}
              variant="contained"
              color="primary"
              data-testid="CreateEditBookingDrawer-Next"
            >
              {ctIntl.formatMessage({ id: 'Next' })}
            </Button>
          </>
        ))
        // Final step - Back and Save
        .with(2, () => (
          <>
            <Button
              onClick={handleBack}
              variant="outlined"
              color="secondary"
              data-testid="CreateEditBookingDrawer-Back"
            >
              {ctIntl.formatMessage({ id: 'Back' })}
            </Button>
            <Button
              color="primary"
              variant="contained"
              onClick={submitForm}
              disabled={
                !isFormValid ||
                (mode === 'create' && !isDirty) ||
                onSubmitLoading ||
                disabled
              }
              loading={onSubmitLoading}
              data-testid="CreateEditBookingDrawer-Save"
            >
              {ctIntl.formatMessage({ id: 'Save' })}
            </Button>
          </>
        ))
        .exhaustive()}
    >
      {match(currentStep)
        .with(0, () => (
          <StepOne
            control={control}
            bookingPurposeOptions={bookingPurposeOptions}
            disabled={disabled}
          />
        ))
        .with(1, () => (
          <StepTwo
            control={control}
            locationOptions={locationOptions}
            maxDateTimeInAdvance={maxDateTimeInAdvance}
            carpoolMaximumBookingTime={carpoolMaximumBookingTime}
            carpoolMaximumBookingTimeUnit={carpoolMaximumBookingTimeUnit}
            disabled={disabled}
            trigger={trigger}
            setFormValue={setFormValue}
          />
        ))
        .with(2, () => (
          <StepThree
            control={control}
            setFormValue={setFormValue}
            vehicleTypeOptions={vehicleTypeOptions}
            disabled={disabled}
            isKeyCollected={isKeyCollected}
            attachmentsQuery={attachmentsQuery}
            mode={mode}
            vehicleIdExists={vehicleIdExists}
          />
        ))
        .exhaustive()}
    </BookingDrawerLayout>
  )
}
