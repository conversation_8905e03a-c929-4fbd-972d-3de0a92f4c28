import { useEffect, useMemo } from 'react'
import {
  Alert,
  Autocomplete,
  Box,
  Chip,
  CircularProgress,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  Stack,
  styled,
  TextField,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import {
  Controller,
  useWatch,
  type Control,
  type ControllerRenderProps,
  type UseFormSetValue,
} from 'react-hook-form'

import { getFacilitiesTranslatorFn } from 'duxs/user'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { DriverSingleSelect } from 'src/modules/shared/DriverSingleSelect'
import UserAutocompleteRHF from 'src/modules/shared/UserAutocompleteRHF'
import { useTypedSelector } from 'src/redux-hooks'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import type useBookingAttachmentsQuery from './api/useBookingAttachmentsQuery'
import useCreateEquipmentMutation from './api/useCreateEquipmentMutation'
import ImageUploader, { type UploadedImageData } from './components/ImageUploader'
import {
  DRIVER_SELECTION,
  DRIVER_SELECTION_OPTIONS,
  EQUIPMENT_TYPE,
  EQUIPMENT_TYPE_OPTIONS,
  JOURNEY_TYPE,
  VEHICLE_COMMANDER_SELECTION,
  VEHICLE_COMMANDER_SELECTION_OPTIONS,
} from './constant'
import useAvailableVehicleOptions from './hooks/useAvailableVehicleOptions'
import { useBookingOptions } from './hooks/useBookingOptions'
import type { BookingFormSchema } from './schema'
import type {
  DrawerMode,
  EquipmentAutocompleteOption,
  VehicleTypeAutocompleteOption,
} from './types'
import {
  CREATE_NEW_DISPLAY,
  createNewEquipmentOption,
  isCreateNewEquipmentOption,
  transformAttachmentsToExistingData,
} from './utils'

type StepThreeProps = {
  control: Control<BookingFormSchema>
  setFormValue: UseFormSetValue<BookingFormSchema>
  vehicleTypeOptions: {
    array: Array<VehicleTypeAutocompleteOption>
    byId: Map<number, VehicleTypeAutocompleteOption>
  }
  disabled: boolean
  isKeyCollected: boolean
  attachmentsQuery?: ReturnType<typeof useBookingAttachmentsQuery>
  mode: DrawerMode
  vehicleIdExists?: boolean
}

const StepThree = ({
  control,
  setFormValue,
  vehicleTypeOptions,
  disabled,
  isKeyCollected,
  attachmentsQuery,
  mode,
  vehicleIdExists = false,
}: StepThreeProps) => {
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const createEquipmentMutation = useCreateEquipmentMutation()

  // Watch for driver and vehicle commander selection to show/hide conditional fields
  const driverSelection = useWatch({ control, name: 'driverSelection' })
  const vehicleCommanderSelection = useWatch({
    control,
    name: 'vehicleCommanderSelection',
  })
  const journeyType = useWatch({ control, name: 'journeyType' })
  const equipmentType = useWatch({ control, name: 'equipmentType' })
  const uploadedImages = useWatch({ control, name: 'uploadedImages' })
  const equipmentAttachmentIds = useWatch({ control, name: 'equipmentAttachmentIds' })

  // get the vehicle options
  const selectedVehicleId = useWatch({ name: 'vehicleId', control })
  const selectedVehicleTypeId = useWatch({ name: 'vehicleTypeId', control })
  const selectedDriverId = useWatch({ name: 'driverId', control })
  const selectedLocationId = useWatch({ name: 'pickupLocation', control })
  const selectedPurposeOfRequestId = useWatch({ name: 'purposeOfRequest', control })

  const facilityLabel = translateFacilitiesTerm('Facility')

  const { carpoolOptionsData, equipmentOptions, isPending } = useBookingOptions()
  const {
    availableVehicleOptions,
    emptyVehicleErrorMessage,
    isPending: isLoadingVehicleOptions,
  } = useAvailableVehicleOptions({
    isLoadingCarpoolOptionsData: isPending,
    selectedVehicleId,
    selectedVehicleTypeId,
    carpoolOptionsData,
    selectedDriverId,
    selectedPurposeOfRequestId,
    selectedLocationId,
    facilityLabel,
    showSelectedVehicle: mode !== 'approve',
  })

  // Filter existing attachments based on equipmentAttachmentIds
  const filteredExistingAttachments = useMemo(() => {
    if (!attachmentsQuery?.data) return []

    const existingAttachments = transformAttachmentsToExistingData(
      attachmentsQuery.data,
    )
    return existingAttachments.filter((attachment) =>
      equipmentAttachmentIds.includes(attachment.id),
    )
  }, [attachmentsQuery?.data, equipmentAttachmentIds])

  // Custom filter function for equipments
  const filterEquipmentOptions = (
    options: Array<EquipmentAutocompleteOption>,
    params: {
      inputValue: string
      getOptionLabel: (option: EquipmentAutocompleteOption) => string
    },
  ): Array<EquipmentAutocompleteOption> => {
    const { inputValue } = params

    if (!inputValue) return options

    const lowercaseSearchTerm = inputValue.toLowerCase()
    const filtered = options.filter((option) =>
      option.label.toLowerCase().includes(lowercaseSearchTerm),
    )

    // If no exact match and searchTerm is not empty, add "create new" option
    const hasExactMatch = options.some(
      (option) => option.label.toLowerCase() === lowercaseSearchTerm,
    )

    if (!hasExactMatch && inputValue.trim()) {
      filtered.push(createNewEquipmentOption(inputValue))
    }

    return filtered
  }

  // Handle equipment selection including creation
  const handleEquipmentChange = async (
    newValue: Array<EquipmentAutocompleteOption>,
    field: ControllerRenderProps<BookingFormSchema, 'equipmentIds'>,
  ) => {
    const createNewOption = newValue.find((o) => isCreateNewEquipmentOption(o))

    if (createNewOption) {
      try {
        const response = await createEquipmentMutation.mutateAsync({
          accessoryTypeId: 1,
          name: createNewOption.originalValue,
          description: createNewOption.originalValue,
        })

        if (response) {
          const existingIds = field.value || []
          const newIds = [...existingIds, response.id]
          field.onChange(newIds)
        }
      } catch (error) {
        if (ENV.NODE_ENV === 'development') {
          console.error('Failed to create equipment:', error)
        }
      }
    } else {
      // Normal selection without creation
      const selectedIds = newValue
        .filter(
          (option): option is EquipmentAutocompleteOption =>
            !isCreateNewEquipmentOption(option),
        )
        .map((option) => option.id)
      field.onChange(selectedIds)
    }
  }

  const handleEquipmentTypeChange = (
    newType: BookingFormSchema['equipmentType'],
    field: ControllerRenderProps<BookingFormSchema, 'equipmentType'>,
  ) => {
    field.onChange(newType)
    if (newType === EQUIPMENT_TYPE.NO_EQUIPMENT) {
      // Revoke object URLs to prevent memory leaks for temporary uploads only
      for (const image of uploadedImages) {
        URL.revokeObjectURL(image.previewUrl)
      }
      setFormValue('equipmentIds', [], { shouldValidate: true })
      setFormValue('uploadedImages', [], { shouldValidate: true })
      setFormValue('equipmentAttachmentIds', [], { shouldValidate: true })
    }
  }

  const handleExistingAttachmentRemove = (attachmentId: number) => {
    const updatedIds = equipmentAttachmentIds.filter((id) => id !== attachmentId)
    setFormValue('equipmentAttachmentIds', updatedIds, { shouldValidate: true })
  }

  // Set equipmentAttachmentIds after form initialization
  useEffect(() => {
    if (
      mode !== 'create' &&
      attachmentsQuery?.data &&
      attachmentsQuery.data.length > 0
    ) {
      const existingAttachments = transformAttachmentsToExistingData(
        attachmentsQuery.data,
      )
      const attachmentIds = existingAttachments.map((att) => att.id)
      setFormValue('equipmentAttachmentIds', attachmentIds, {
        shouldValidate: false,
        shouldDirty: false,
      })
    }
  }, [mode, setFormValue, attachmentsQuery?.data])

  const RenderedVehicleIdComponent = useMemo(
    () => (
      <Controller
        control={control}
        name="vehicleId"
        render={({ field, fieldState }) => (
          <Autocomplete
            disabled={isKeyCollected || disabled}
            size="small"
            data-testid="StepThree-VehicleId"
            {...getAutocompleteVirtualizedProps({
              options: availableVehicleOptions.array,
            })}
            onChange={(_, newValue) => {
              setFormValue(field.name, newValue ? newValue.id : null, {
                shouldValidate: true,
              })
            }}
            value={
              field.value ? availableVehicleOptions.byId.get(field.value) ?? null : null
            }
            renderInput={(params) => (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TextField
                  {...params}
                  required
                  label={ctIntl.formatMessage({ id: 'Vehicle registrations' })}
                  helperText={ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  })}
                  error={!!fieldState.error}
                />
                {isLoadingVehicleOptions && <CircularProgress size={20} />}
              </Box>
            )}
          />
        )}
      />
    ),
    [
      availableVehicleOptions.array,
      availableVehicleOptions.byId,
      control,
      disabled,
      isKeyCollected,
      isLoadingVehicleOptions,
      setFormValue,
    ],
  )

  return (
    <Stack
      gap={3}
      mb={3}
    >
      <Box>
        <Typography mb={2}>{ctIntl.formatMessage({ id: 'Vehicle' })}</Typography>

        {/* NOTE: when vehicle id exists already, override the vehicle type selection
            https://gitlab.cartrack.com/cartrack-base/cartrack-special-projects/cartrack-tfms-dev/tfms-web/-/issues/84 */}
        {vehicleIdExists && mode !== 'approve' ? (
          RenderedVehicleIdComponent
        ) : (
          <Controller
            control={control}
            name="vehicleTypeId"
            render={({ field, fieldState }) => (
              <Autocomplete
                size="small"
                data-testid="StepThree-VehicleType"
                {...getAutocompleteVirtualizedProps({
                  options: vehicleTypeOptions.array,
                })}
                onChange={(_, newValue) =>
                  field.onChange(newValue ? newValue.id : null)
                }
                value={
                  field.value ? vehicleTypeOptions.byId.get(field.value) ?? null : null
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    required
                    label={ctIntl.formatMessage({ id: 'Vehicle type' })}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                    error={!!fieldState.error}
                  />
                )}
                disabled={isKeyCollected || disabled}
              />
            )}
          />
        )}

        <Box mt={2}>
          <Controller
            control={control}
            name="numberOfPassengers"
            render={({ field, fieldState }) => (
              <TextField
                {...field}
                size="small"
                type="number"
                required
                disabled={disabled}
                label="No of passengers"
                value={field.value || ''}
                onChange={(e) => {
                  const value = e.target.value
                  field.onChange(value ? Number.parseInt(value, 10) : null)
                }}
                inputProps={{ min: 1 }}
                helperText={ctIntl.formatMessage({
                  id: fieldState.error?.message ?? '',
                })}
                error={!!fieldState.error}
                fullWidth
                data-testid="StepThree-NumberOfPassengers"
              />
            )}
          />
        </Box>

        {mode === 'approve' && <Box mt={2}>{RenderedVehicleIdComponent}</Box>}

        {mode === 'approve' && !disabled && (
          <>
            <IntlTypography
              sx={(theme) => ({ pt: 1, color: theme.palette.error.main })}
              msgProps={{ id: emptyVehicleErrorMessage.current?.message ?? '' }}
            />

            {(emptyVehicleErrorMessage.current?.fieldMessages ?? []).map(
              (fieldMessage) => (
                <IntlTypography
                  key={fieldMessage}
                  msgProps={{ id: fieldMessage }}
                  sx={(theme) => ({
                    pt: 1,
                    color: theme.palette.error.main,
                    ml: 3,
                    display: 'list-item',
                  })}
                />
              ),
            )}
          </>
        )}
      </Box>

      <Box>
        <IntlTypography
          mb={2}
          msgProps={{ id: 'Driver' }}
        />

        {/* if it's in approve mode, driver selection should be 'specific' */}
        {mode === 'approve' ? null : (
          <Controller
            control={control}
            name="driverSelection"
            render={({ field, fieldState }) => (
              <>
                <StyledRadioGroup
                  {...field}
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(
                      e.target.value as BookingFormSchema['driverSelection'],
                    )
                    // Clear driverId when not selecting specific driver
                    if (e.target.value !== DRIVER_SELECTION.SPECIFIC) {
                      setFormValue('driverId', null, { shouldValidate: false })
                    }
                  }}
                  data-testid="StepThree-DriverSelection"
                >
                  {DRIVER_SELECTION_OPTIONS.map((option) => (
                    <FormControlLabel
                      data-testid={`StepThree-DriverSelection-${option.value}`}
                      disabled={disabled}
                      key={option.value}
                      value={option.value}
                      control={<Radio />}
                      label={option.label}
                    />
                  ))}
                </StyledRadioGroup>
                {!!fieldState.error?.message && (
                  <FormHelperText
                    error
                    sx={{ pb: 1 }}
                  >
                    {ctIntl.formatMessage({ id: fieldState.error?.message })}
                  </FormHelperText>
                )}
              </>
            )}
          />
        )}

        {/* Show driver autocomplete only when "Specific" is selected */}
        {driverSelection === DRIVER_SELECTION.SPECIFIC && (
          <Controller
            control={control}
            name="driverId"
            render={({ field, fieldState }) => (
              <DriverSingleSelect
                disabled={isKeyCollected || disabled}
                size="small"
                onChange={(newValue) => {
                  field.onChange(newValue ? newValue.value : null)
                }}
                driverId={field.value ?? null}
                promptName={ctIntl.formatMessage({ id: 'Select Driver' })}
                error={fieldState.error}
                data-testid="StepThree-DriverSelect"
              />
            )}
          />
        )}
      </Box>

      {/* Vehicle Commander Section */}
      <Box>
        <Typography
          mb={2}
          data-testid="StepThree-VehicleCommanderTitle"
        >
          Vehicle Commander
        </Typography>

        {(journeyType === JOURNEY_TYPE.SINGLE ||
          journeyType === JOURNEY_TYPE.RETURN) && (
          <Alert
            data-testid="StepThree-VehicleCommanderAlert"
            severity="info"
            sx={{ mb: 2 }}
          >
            Vehicle commander is required for one-way journey (single journey and return
            journey)
          </Alert>
        )}

        {/* if it's in approve mode, vehicle commander selection should be 'specific' */}
        {mode === 'approve' ? null : (
          <Controller
            control={control}
            name="vehicleCommanderSelection"
            render={({ field, fieldState }) => (
              <>
                <StyledRadioGroup
                  {...field}
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(
                      e.target.value as BookingFormSchema['vehicleCommanderSelection'],
                    )
                    // Clear vehicleCommanderId when not selecting specific commander
                    if (e.target.value !== VEHICLE_COMMANDER_SELECTION.SPECIFIC) {
                      setFormValue('vehicleCommanderId', null, {
                        shouldValidate: false,
                      })
                    }
                  }}
                  data-testid="StepThree-VehicleCommanderSelection"
                >
                  {VEHICLE_COMMANDER_SELECTION_OPTIONS.map((option) => (
                    <FormControlLabel
                      disabled={disabled}
                      key={option.value}
                      value={option.value}
                      control={<Radio />}
                      label={option.label}
                      data-testid={`StepThree-VehicleCommanderSelection-${option.value}`}
                    />
                  ))}
                </StyledRadioGroup>
                {!!fieldState.error?.message && (
                  <FormHelperText
                    error
                    sx={{ pb: 1 }}
                  >
                    {ctIntl.formatMessage({ id: fieldState.error?.message })}
                  </FormHelperText>
                )}
              </>
            )}
          />
        )}

        {vehicleCommanderSelection === VEHICLE_COMMANDER_SELECTION.SPECIFIC && (
          <UserAutocompleteRHF
            control={control}
            name="vehicleCommanderId"
            label="Select Vehicle Commander"
            disabled={disabled}
            required={
              journeyType === JOURNEY_TYPE.SINGLE || journeyType === JOURNEY_TYPE.RETURN
            }
            data-testid="StepThree-VehicleCommanderSelect"
          />
        )}
      </Box>

      {/* Equipments Section */}
      <Box>
        <Typography
          mb={2}
          data-testid="StepThree-EquipmentsTitle"
        >
          Equipments
        </Typography>

        <Controller
          control={control}
          name="equipmentType"
          render={({ field, fieldState }) => (
            <>
              <StyledRadioGroup
                {...field}
                value={field.value}
                onChange={(e) => {
                  handleEquipmentTypeChange(
                    e.target.value as BookingFormSchema['equipmentType'],
                    field,
                  )
                }}
                sx={{ flexDirection: 'column' }}
                data-testid="StepThree-EquipmentType"
              >
                {EQUIPMENT_TYPE_OPTIONS.map((option) => (
                  <FormControlLabel
                    disabled={disabled}
                    key={option.value}
                    value={option.value}
                    control={<Radio />}
                    label={option.label}
                    data-testid={`StepThree-EquipmentType-${option.value}`}
                  />
                ))}
              </StyledRadioGroup>
              {!!fieldState.error?.message && (
                <FormHelperText
                  error
                  sx={{ pb: 1 }}
                >
                  {ctIntl.formatMessage({ id: fieldState.error?.message })}
                </FormHelperText>
              )}
            </>
          )}
        />

        {equipmentType !== EQUIPMENT_TYPE.NO_EQUIPMENT && (
          <>
            <Controller
              control={control}
              name="equipmentIds"
              render={({ field, fieldState }) => (
                <Autocomplete<EquipmentAutocompleteOption, true>
                  disabled={disabled || createEquipmentMutation.isPending}
                  size="small"
                  multiple
                  options={equipmentOptions.array}
                  data-testid="StepThree-EquipmentSelect"
                  onChange={(_, newValue) => {
                    handleEquipmentChange(newValue, field)
                  }}
                  value={
                    field.value
                      ? field.value
                          .map((id) => equipmentOptions.byId.get(id))
                          .filter(
                            (option): option is EquipmentAutocompleteOption =>
                              option !== undefined,
                          )
                      : []
                  }
                  filterOptions={filterEquipmentOptions}
                  getOptionLabel={(option) => option.label}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  renderOption={(props, option) => (
                    <li {...props}>
                      {isCreateNewEquipmentOption(option) ? (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            fontStyle: 'italic',
                          }}
                        >
                          {createEquipmentMutation.isPending ? (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <CircularProgress
                                size={16}
                                thickness={4}
                                sx={{ color: 'primary.main' }}
                              />
                              {CREATE_NEW_DISPLAY.LOADING_TEXT}
                            </Box>
                          ) : (
                            option.label
                          )}
                        </Box>
                      ) : (
                        option.label
                      )}
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      required
                      label="Equipments"
                      helperText={ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      })}
                      error={!!fieldState.error}
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        {...getTagProps({ index })}
                        key={option.id}
                        label={option.label}
                        size="small"
                      />
                    ))
                  }
                />
              )}
            />

            {/* Image Uploader with attachment loading state */}
            <Controller
              control={control}
              name="uploadedImages"
              render={({ field }) => (
                <Box>
                  {attachmentsQuery?.status === 'pending' && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        mt: 2,
                        mb: 1,
                      }}
                    >
                      <CircularProgress size={16} />
                      <Typography
                        variant="caption"
                        color="text.secondary"
                      >
                        Loading existing attachments...
                      </Typography>
                    </Box>
                  )}

                  {attachmentsQuery?.status === 'error' && (
                    <Alert
                      severity="warning"
                      sx={{ mt: 2, mb: 1 }}
                    >
                      Failed to load attachments
                    </Alert>
                  )}

                  <ImageUploader
                    uploadedImages={field.value}
                    onBatchImagesAdd={(images: Array<UploadedImageData>) => {
                      const updatedImages = [...field.value, ...images]
                      field.onChange(updatedImages)
                    }}
                    onImageRemove={(guid: string) => {
                      const updatedImages = field.value.filter(
                        (img) => img.guid !== guid,
                      )
                      field.onChange(updatedImages)
                    }}
                    existingAttachments={filteredExistingAttachments}
                    onExistingAttachmentRemove={handleExistingAttachmentRemove}
                    maxFiles={5}
                    disabled={disabled}
                  />
                </Box>
              )}
            />
          </>
        )}
      </Box>

      <Box>
        <IntlTypography
          msgProps={{ id: 'Remarks' }}
          data-testid="StepThree-RemarksTitle"
        />
        <TextFieldControlled
          ControllerProps={{ control, name: 'remarks' }}
          sx={{
            width: '100%',
            '& .MuiFormHelperText-root.Mui-error': {
              position: 'absolute',
              top: '100%',
            },
          }}
          variant="standard"
          label={ctIntl.formatMessage({ id: 'Remarks' })}
          data-testid="RemarksInput"
          disabled={disabled}
        />
      </Box>
    </Stack>
  )
}

export default StepThree

const StyledRadioGroup = styled(RadioGroup)({
  mb: 1,
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
})
