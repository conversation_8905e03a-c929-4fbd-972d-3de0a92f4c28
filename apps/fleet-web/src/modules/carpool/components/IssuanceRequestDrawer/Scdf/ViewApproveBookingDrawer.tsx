import { useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Button, CircularProgressDelayedCentered, Divider } from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { useForm, useWatch } from 'react-hook-form'
import { match, P } from 'ts-pattern'

import type { CarpoolBookingId, ClientUserId, DriverId, VehicleId } from 'api/types'
import {
  getCarpoolAllowBackDateBooking,
  getCarpoolApproveBookings,
  getCarpoolDeclineBookings,
} from 'duxs/user-sensitive-selectors'
import { useUsersQuery } from 'src/modules/api/useUsersQuery'
import { useTypedSelector } from 'src/redux-hooks'

import { ctIntl } from 'cartrack-ui-kit'
import { useCarpoolBookingTimeRules } from '../../../Settings/Rules/api/queries'
import type { BookingStatus } from '../../../utils/constants'
import useBookingAttachmentsQuery from './api/useBookingAttachmentsQuery'
import useBookingDetailsQuery from './api/useBookingDetailsQuery'
import BookingDrawerLayout from './components/BookingDrawerLayout'
import RejectBookingModal from './components/RejectBookingModal'
import { DRIVER_SELECTION, VEHICLE_COMMANDER_SELECTION } from './constant'
import { useBookingOptions } from './hooks/useBookingOptions'
import { useVehicleTypeOptions } from './hooks/useVehicleTypeOptions'
import {
  generateBookingSchema,
  type BookingFormSchema,
  type ValidSchema,
} from './schema'
import StepOne from './StepOne'
import StepThree from './StepThree'
import StepTwo from './StepTwo'
import type { RejectBookingFormData } from './types'
import { mapBookingToFormValues } from './utils'

type BaseProps = {
  onClose: () => void
  bookingId: CarpoolBookingId
}

type ViewProps = {
  mode: 'view'
}

export type ApproveBookingProps = ValidSchema & {
  vehicleId: VehicleId
  driverId: DriverId
  vehicleCommanderId: ClientUserId
  requestClientUserId: ClientUserId
}

type ApproveProps = {
  mode: 'approve'
  onSubmit: (values: ApproveBookingProps) => void
  onReject: (data: RejectBookingFormData) => void
  onApprovalLoading: boolean
  onRejectLoading: boolean
}

type Props = BaseProps & (ViewProps | ApproveProps)

type BaseContentProps = BaseProps & {
  bookingStatusId: BookingStatus
  isKeyCollected: boolean
  attachmentsQuery: ReturnType<typeof useBookingAttachmentsQuery>
  initialValues: BookingFormSchema
}

type ContentProps = BaseContentProps & (ViewProps | ApproveProps)

export default function ViewApproveBookingDrawer(props: Props) {
  const { onClose, bookingId, ...rest } = props
  const attachmentsQuery = useBookingAttachmentsQuery(bookingId)
  const bookingDetailsQuery = useBookingDetailsQuery(bookingId)

  return match([bookingDetailsQuery, attachmentsQuery])
    .with(
      P.when(([bQuery, aQuery]) => bQuery.isPending || aQuery.isPending),
      () => <CircularProgressDelayedCentered />,
    )
    .with(
      [{ status: 'success' }, { status: 'success' }],
      ([{ data: bookingDetails }, aQuery]) => (
        <ViewApproveBookingDrawerContent
          onClose={onClose}
          bookingId={bookingId}
          bookingStatusId={bookingDetails.statusId.toString() as BookingStatus}
          isKeyCollected={!!bookingDetails.keyReturnTs}
          attachmentsQuery={aQuery}
          {...(rest.mode === 'approve'
            ? {
                mode: 'approve',
                onSubmit: rest.onSubmit,
                onReject: rest.onReject,
                onApprovalLoading: rest.onApprovalLoading,
                onRejectLoading: rest.onRejectLoading,
                initialValues: {
                  ...mapBookingToFormValues({
                    bookingDetails,
                    isApproveMode: true,
                  }),
                  // in approve mode, driver and vehicle commander selection should be 'specific'
                  driverSelection: DRIVER_SELECTION.SPECIFIC,
                  vehicleCommanderSelection: VEHICLE_COMMANDER_SELECTION.SPECIFIC,
                },
              }
            : {
                mode: 'view',
                initialValues: mapBookingToFormValues({ bookingDetails }),
              })}
        />
      ),
    )
    .otherwise(() => {
      onClose()
      return null
    })
}

function ViewApproveBookingDrawerContent(props: ContentProps) {
  const { mode, onClose, initialValues, bookingId, isKeyCollected, attachmentsQuery } =
    props

  const [showRejectModal, setShowRejectModal] = useState(false)
  const carpoolAllowBackDateBooking = useTypedSelector(getCarpoolAllowBackDateBooking)
  const carpoolApproveBookings = useTypedSelector(getCarpoolApproveBookings)
  const carpoolDeclineBookings = useTypedSelector(getCarpoolDeclineBookings)
  const {
    carpoolBookingInAdvance,
    carpoolBookingInAdvanceUnit,
    carpoolMaximumBookingTime,
    carpoolMaximumBookingTimeUnit,
  } = useCarpoolBookingTimeRules()
  // FIXME: use this query instead of just calling it
  // Preload the user query data to use
  useUsersQuery()

  // Get all booking options using the custom hook
  const { locationOptions, bookingPurposeOptions } = useBookingOptions()

  const validSchema = useMemo(
    () =>
      generateBookingSchema({
        carpoolBookingInAdvance,
        carpoolBookingInAdvanceUnit,
        carpoolMaximumBookingTime,
        carpoolMaximumBookingTimeUnit,
        carpoolAllowBackDateBooking,
        bookingPurposeByIdMap: bookingPurposeOptions.byId,
        mode,
      }),
    [
      carpoolBookingInAdvance,
      carpoolBookingInAdvanceUnit,
      carpoolMaximumBookingTime,
      carpoolMaximumBookingTimeUnit,
      carpoolAllowBackDateBooking,
      bookingPurposeOptions.byId,
      mode,
    ],
  )

  // Setup form with the complete schema for all steps
  const {
    control,
    handleSubmit,
    setValue: setFormValue,
    trigger,
    formState: { isValid: isFormValid, errors },
  } = useForm<BookingFormSchema>({
    resolver: zodResolver(validSchema),
    mode: 'onChange',
    defaultValues: initialValues,
  })

  const vehicleIdExists = mode === 'view' && initialValues.vehicleId !== null

  const selectedPurposeOfRequest = useWatch({ name: 'purposeOfRequest', control })
  const submitForm = handleSubmit((values) => {
    const formValues = values as ApproveBookingProps
    if (mode === 'approve') {
      props.onSubmit({
        ...formValues,
        requestClientUserId: (initialValues.requestedForClientUserId ??
          '') as ClientUserId,
      })
    }
  })

  const handleRejectSubmit = (data: RejectBookingFormData) => {
    if (mode === 'approve') {
      props.onReject(data)
      setShowRejectModal(false)
    }
  }

  // Get vehicle type options that depend on selected purpose
  const vehicleTypeOptions = useVehicleTypeOptions(selectedPurposeOfRequest)

  const maxDateTimeInAdvance =
    carpoolBookingInAdvance > 0
      ? DateTime.now().plus({
          [carpoolBookingInAdvanceUnit]: carpoolBookingInAdvance,
        })
      : undefined

  const getHeaderTitle = () =>
    mode === 'approve' ? 'Approve/Reject Booking' : 'View Booking'

  return (
    <>
      <BookingDrawerLayout
        onClose={onClose}
        title={getHeaderTitle()}
        bookingId={bookingId}
        actionButtons={
          <>
            <Button
              onClick={onClose}
              variant="outlined"
              color="secondary"
              data-testid="ViewApproveBookingDrawer-Cancel"
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            {mode === 'approve' && (
              <Box sx={{ display: 'flex', gap: 1 }}>
                {carpoolDeclineBookings && (
                  <Button
                    onClick={() => setShowRejectModal(true)}
                    variant="contained"
                    color="error"
                    data-testid="ViewApproveBookingDrawer-Reject"
                  >
                    {ctIntl.formatMessage({ id: 'Reject' })}
                  </Button>
                )}
                {carpoolApproveBookings && (
                  <Button
                    onClick={submitForm}
                    variant="contained"
                    color="success"
                    loading={props.onApprovalLoading}
                    disabled={
                      // FIXME: it's weird that form is invalid while errors empty
                      !(isFormValid || isEmpty(errors)) || props.onApprovalLoading
                    }
                    data-testid="ViewApproveBookingDrawer-Approve"
                  >
                    {ctIntl.formatMessage({ id: 'Approve' })}
                  </Button>
                )}
              </Box>
            )}
          </>
        }
      >
        <StepOne
          control={control}
          bookingPurposeOptions={bookingPurposeOptions}
          disabled={mode === 'view'}
        />
        <Divider sx={{ my: 3 }} />
        <StepTwo
          control={control}
          locationOptions={locationOptions}
          maxDateTimeInAdvance={maxDateTimeInAdvance}
          carpoolMaximumBookingTime={carpoolMaximumBookingTime}
          carpoolMaximumBookingTimeUnit={carpoolMaximumBookingTimeUnit}
          disabled={mode === 'view'}
          trigger={trigger}
          setFormValue={setFormValue}
        />
        <Divider sx={{ my: 3 }} />
        <StepThree
          control={control}
          setFormValue={setFormValue}
          vehicleTypeOptions={vehicleTypeOptions}
          disabled={mode === 'view'}
          isKeyCollected={isKeyCollected}
          attachmentsQuery={attachmentsQuery}
          mode={mode}
          vehicleIdExists={vehicleIdExists}
        />
      </BookingDrawerLayout>
      {showRejectModal && mode === 'approve' && (
        <RejectBookingModal
          bookingId={bookingId}
          onClose={() => setShowRejectModal(false)}
          onConfirm={handleRejectSubmit}
          isLoading={(props as ApproveProps).onRejectLoading}
        />
      )}
    </>
  )
}
