import { useRef, useState } from 'react'
import {
  Autocomplete,
  Box,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import BookIcon from '@mui/icons-material/Book'
import DeleteIcon from '@mui/icons-material/Delete'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import ModeEditIcon from '@mui/icons-material/ModeEdit'
import { useDrag, useDrop } from 'react-dnd'
import { Controller, type Control } from 'react-hook-form'

import { ctIntl } from 'cartrack-ui-kit'
import type { BookingFormSchema } from '../schema'
import type { UnifiedLocationOption } from '../types'
import {
  createFreeTextLocationOption,
  isFreeTextLocation,
  isFreeTextOptionLabel,
  isStandardLocation,
} from '../utils'

// Define item type for react-dnd
export const ItemTypes = {
  JOURNEY: 'journey',
}

type DraggableItemProps = {
  index: number
  type: string
}

type DraggableJourneyProps = {
  index: number
  control: Control<BookingFormSchema>
  moveJourney: (dragIndex: number, hoverIndex: number) => void
  removeJourney: (index: number) => void
  disabled: boolean
  unifiedLocationOptions: {
    array: Array<UnifiedLocationOption>
    byId: Map<string, UnifiedLocationOption>
  }
  canMove: boolean
  canRemove: boolean
}

const DraggableJourney = ({
  index,
  control,
  moveJourney,
  removeJourney,
  disabled,
  unifiedLocationOptions,
  canMove,
  canRemove,
}: DraggableJourneyProps) => {
  const ref = useRef<HTMLDivElement>(null)
  const [isFreeTextMode, setIsFreeTextMode] = useState(false)

  const [, drop] = useDrop<DraggableItemProps, void, { isOver: boolean }>({
    accept: ItemTypes.JOURNEY,
    hover(item, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      if (dragIndex === hoverIndex) {
        return
      }

      const hoverBoundingRect = ref.current.getBoundingClientRect()
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
      const clientOffset = monitor.getClientOffset()

      if (!clientOffset) {
        return
      }

      const hoverClientY = clientOffset.y - hoverBoundingRect.top

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      moveJourney(dragIndex, hoverIndex)
      // eslint-disable-next-line no-param-reassign
      item.index = hoverIndex
    },
  })

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.JOURNEY,
    item: { type: ItemTypes.JOURNEY, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  drag(drop(ref))

  // Helper to get option key for lookup
  const getOptionKey = (option: UnifiedLocationOption): string => {
    if (isStandardLocation(option)) return `location-${option.locationId}`
    if (isFreeTextLocation(option)) return `freeText-${option.value}`
    return `additionalLocation-${option.additionalLocationId}`
  }

  // Helper to find option from Journey value
  const findOptionFromValue = (
    journey: BookingFormSchema['journeys'][number],
  ): UnifiedLocationOption | null => {
    if (!journey) return null

    if (journey.type === 'freeText') {
      return {
        type: 'freeText',
        value: journey.value,
        label: journey.value,
      }
    }

    if (!journey.id) return null
    const key = `${journey.type}-${journey.id}`
    return unifiedLocationOptions.byId.get(key) || null
  }

  // Custom filter function for locations with free text support
  const filterLocationOptions = (
    options: Array<UnifiedLocationOption>,
    params: {
      inputValue: string
      getOptionLabel: (option: UnifiedLocationOption) => string
    },
  ): Array<UnifiedLocationOption> => {
    const { inputValue } = params

    if (!inputValue) return options

    const lowercaseSearchTerm = inputValue.toLowerCase()

    // Filter existing options
    const filtered = options.filter((option) =>
      option.label.toLowerCase().includes(lowercaseSearchTerm),
    )

    // In free text mode, add "create new" option if no exact match and searchTerm is not empty
    if (isFreeTextMode) {
      const hasExactMatch = options.some(
        (option) =>
          option.label.toLowerCase() === lowercaseSearchTerm &&
          !isFreeTextOptionLabel(option.label),
      )

      if (!hasExactMatch) {
        filtered.push(createFreeTextLocationOption(inputValue))
      }
    }

    return filtered
  }

  const handleToggleMode = () => {
    setIsFreeTextMode((currentMode) => !currentMode)
  }

  return (
    <Box
      ref={ref}
      data-testid={`DraggableJourney-${index}`}
      sx={{
        opacity: isDragging ? 0.5 : 1,
        mb: 2,
        display: 'flex',
        alignItems: 'flex-start',
      }}
    >
      <Controller
        control={control}
        name={`journeys.${index}`}
        render={({ field, fieldState }) => (
          <Autocomplete<UnifiedLocationOption>
            data-testid={`DraggableJourney-LocationAutocomplete-${index}`}
            size="small"
            disabled={disabled}
            options={unifiedLocationOptions.array}
            groupBy={(option) => {
              if (isFreeTextLocation(option)) return 'New Additional Location'
              if (isStandardLocation(option)) return 'Locations'
              return 'Additional Locations'
            }}
            filterOptions={filterLocationOptions}
            onChange={(_, newValue) => {
              if (!newValue) {
                field.onChange({
                  type: 'location',
                  id: null, // invalid value
                })
                return
              }

              const journeyValue = (() => {
                if (isStandardLocation(newValue)) {
                  return {
                    type: 'location' as const,
                    id: newValue.locationId,
                  }
                }

                if (isFreeTextLocation(newValue)) {
                  return {
                    type: 'freeText' as const,
                    value: newValue.value,
                  }
                }
                return {
                  type: 'additionalLocation' as const,
                  id: newValue.additionalLocationId,
                }
              })()

              field.onChange(journeyValue)
            }}
            value={findOptionFromValue(field.value)}
            isOptionEqualToValue={(option, value) =>
              getOptionKey(option) === getOptionKey(value)
            }
            renderOption={(props, option) => (
              <li {...props}>
                <Box>
                  <Typography
                    sx={{ fontStyle: isFreeTextLocation(option) ? 'italic' : 'normal' }}
                  >
                    {option.label}
                  </Typography>
                </Box>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Location"
                variant="outlined"
                required
                error={!!fieldState.error}
                helperText={
                  fieldState.error
                    ? ctIntl.formatMessage({
                        id:
                          ('value' in fieldState.error && fieldState.error.value
                            ? (fieldState.error.value as { message: string }).message
                            : fieldState.error.message) ?? '',
                      })
                    : null
                }
                fullWidth
              />
            )}
            sx={{ flex: 1 }}
          />
        )}
      />

      <Tooltip
        title={
          isFreeTextMode
            ? 'Select from Point of Interest (POI) List'
            : 'Enter location manually'
        }
      >
        <IconButton
          data-testid={`DraggableJourney-ToggleMode-${index}`}
          onClick={handleToggleMode}
          disabled={disabled}
        >
          {isFreeTextMode ? <BookIcon /> : <ModeEditIcon />}
        </IconButton>
      </Tooltip>

      {canMove && (
        <IconButton data-testid={`DraggableJourney-DragHandle-${index}`}>
          <DragIndicatorIcon />
        </IconButton>
      )}
      {canRemove && (
        <IconButton
          data-testid={`DraggableJourney-Remove-${index}`}
          onClick={() => removeJourney(index)}
          disabled={disabled}
        >
          <DeleteIcon />
        </IconButton>
      )}
    </Box>
  )
}

export default DraggableJourney
