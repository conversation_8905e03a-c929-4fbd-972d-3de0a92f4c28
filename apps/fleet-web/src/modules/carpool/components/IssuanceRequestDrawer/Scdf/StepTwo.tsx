import { useMemo } from 'react'
import { isEmpty } from 'lodash'
import {
  Autocomplete,
  Box,
  DateTimePicker,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import { DateTime } from 'luxon'
import {
  Controller,
  useWatch,
  type Control,
  type UseFormSetValue,
  type UseFormTrigger,
} from 'react-hook-form'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'

import { getFacilitiesTranslatorFn } from 'duxs/user'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { LIST } from 'src/modules/app/components/routes/list'
import { useTypedSelector } from 'src/redux-hooks'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import useAdditionalLocationsQuery from './api/useAdditionalLocationsQuery'
import JourneysList from './components/JourneysList'
import { JOURNEY_TYPE, JOURNEY_TYPE_OPTIONS } from './constant'
import type { BookingFormSchema } from './schema'
import type { LocationAutocompleteOption, UnifiedLocationOption } from './types'
import { createAdditionalLocationOption, createStandardLocationOption } from './utils'

type StepTwoProps = {
  control: Control<BookingFormSchema>
  locationOptions: {
    array: Array<LocationAutocompleteOption>
    byId: Map<number, LocationAutocompleteOption>
  }
  maxDateTimeInAdvance: DateTime | undefined
  carpoolMaximumBookingTime: number
  carpoolMaximumBookingTimeUnit: string
  disabled: boolean
  trigger: UseFormTrigger<BookingFormSchema>
  setFormValue: UseFormSetValue<BookingFormSchema>
}

const StepTwo = ({
  control,
  locationOptions,
  maxDateTimeInAdvance,
  carpoolMaximumBookingTime,
  carpoolMaximumBookingTimeUnit,
  disabled,
  trigger,
  setFormValue,
}: StepTwoProps) => {
  const history = useHistory()
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const additionalLocationsQuery = useAdditionalLocationsQuery()

  // Get current journey type from the form
  const journeyType = useWatch({
    control,
    name: 'journeyType',
    defaultValue: JOURNEY_TYPE.RETURN,
  })
  const pickupTimeValue = useWatch({ name: 'pickupTime', control })

  // Create unified location options for dropoff/journeys
  const unifiedLocationOptions = useMemo(() => {
    const array: Array<UnifiedLocationOption> = []
    const byId = new Map<string, UnifiedLocationOption>()

    // Add standard locations
    for (const location of locationOptions.array) {
      const option = createStandardLocationOption(location.id, location.label)
      array.push(option)
      byId.set(`location-${location.id}`, option)
    }

    // Add additional locations if available
    if (additionalLocationsQuery.data) {
      for (const additionalLocation of additionalLocationsQuery.data) {
        const option = createAdditionalLocationOption(
          additionalLocation.id,
          additionalLocation.locationName,
        )
        array.push(option)
        byId.set(`additionalLocation-${additionalLocation.id}`, option)
      }
    }

    return { array, byId }
  }, [locationOptions.array, additionalLocationsQuery.data])

  const pickupLocationLabel = useMemo(
    () =>
      match(journeyType)
        .with(JOURNEY_TYPE.RETURN, () => 'Start Location & Destination')
        .with(JOURNEY_TYPE.SINGLE, JOURNEY_TYPE.MULTIPLE, () => 'Start Location')
        .exhaustive(),
    [journeyType],
  )

  const handleJourneyTypeChange = (value: BookingFormSchema['journeyType']) => {
    setFormValue('journeyType', value, { shouldValidate: true })

    // Add default journeys for single and multiple journey types
    if (value === JOURNEY_TYPE.SINGLE || value === JOURNEY_TYPE.MULTIPLE) {
      const defaultJourney = {
        type: 'location' as const,
        id: null,
      }
      setFormValue('journeys', [defaultJourney], { shouldValidate: true })
    } else {
      // Clear journeys for return journey
      setFormValue('journeys', [], { shouldValidate: true })
    }
  }

  return (
    <Stack
      gap={2}
      mb={3}
    >
      <Box>
        <IntlTypography
          mb={2}
          msgProps={{ id: 'scdf.list.addEditBooking.pickup' }}
        />
        <Controller
          name="pickupTime"
          control={control}
          render={({ field, fieldState }) => (
            <Box data-testid="StepTwo-PickupTime">
              <DateTimePicker
                {...field}
                disabled={disabled}
                sx={{ width: '100%' }}
                slotProps={{
                  textField: {
                    error: !!fieldState.error,
                    helperText: ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    }),
                    required: true,
                  },
                }}
                value={field.value ? DateTime.fromJSDate(field.value) : null}
                label={ctIntl.formatMessage({ id: 'Date / Time' })}
                onOpen={() => {
                  // pre-set the value when creation
                  if (!field.value) {
                    setFormValue(
                      'pickupTime',
                      DateTime.now().startOf('hour').toJSDate(),
                      { shouldValidate: true },
                    )
                  }
                }}
                onChange={(value) => {
                  setFormValue('pickupTime', value?.toJSDate() ?? null, {
                    shouldValidate: true,
                  })
                  if (field.value) {
                    trigger('dropoffTime')
                  }
                }}
                maxDateTime={maxDateTimeInAdvance}
              />
            </Box>
          )}
        />
      </Box>

      <Box>
        <IntlTypography
          mb={2}
          msgProps={{ id: 'scdf.list.addEditBooking.dropoff' }}
        />
        <Controller
          name="dropoffTime"
          control={control}
          render={({ field, fieldState }) => (
            <Box data-testid="StepTwo-DropoffTime">
              <DateTimePicker
                {...field}
                sx={{ width: '100%' }}
                disabled={disabled}
                slotProps={{
                  textField: {
                    error: !!fieldState.error,
                    helperText: ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    }),
                    required: true,
                  },
                }}
                value={field.value ? DateTime.fromJSDate(field.value) : null}
                label={ctIntl.formatMessage({ id: 'Date / Time' })}
                onChange={(value) => {
                  setFormValue('dropoffTime', value?.toJSDate() ?? null, {
                    shouldValidate: true,
                  })
                }}
                minDateTime={
                  pickupTimeValue
                    ? DateTime.fromJSDate(pickupTimeValue)
                    : maxDateTimeInAdvance
                }
                maxDateTime={
                  carpoolMaximumBookingTime > 0 && pickupTimeValue
                    ? DateTime.fromJSDate(pickupTimeValue).plus({
                        [carpoolMaximumBookingTimeUnit]: carpoolMaximumBookingTime,
                      })
                    : undefined
                }
              />
            </Box>
          )}
        />
      </Box>

      <Typography>{translateFacilitiesTerm('Facility')}</Typography>

      <Controller
        control={control}
        name="journeyType"
        render={({ field, fieldState }) => (
          <>
            <RadioGroup
              {...field}
              value={field.value}
              onChange={(e) => {
                const newValue = e.target.value as BookingFormSchema['journeyType']
                field.onChange(newValue)
                handleJourneyTypeChange(newValue)
              }}
              sx={{
                mb: 1,
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
              data-testid="StepTwo-JourneyType"
            >
              {JOURNEY_TYPE_OPTIONS.map((option) => (
                <FormControlLabel
                  data-testid={`StepTwo-JourneyType-${option.value}`}
                  disabled={disabled}
                  key={option.value}
                  value={option.value}
                  control={<Radio />}
                  label={option.label}
                />
              ))}
            </RadioGroup>
            {!!fieldState.error?.message && (
              <FormHelperText
                data-testid="StepTwo-JourneyType-Error"
                error
                sx={{ pb: 1 }}
              >
                {ctIntl.formatMessage({ id: fieldState.error?.message })}
              </FormHelperText>
            )}
          </>
        )}
      />

      <Box>
        {/* Pickup Location - Standard locations only */}
        <Controller
          control={control}
          name="pickupLocation"
          render={({ field, fieldState }) => (
            <>
              <Autocomplete
                size="small"
                disabled={disabled}
                data-testid="StepTwo-PickupLocation"
                {...getAutocompleteVirtualizedProps({
                  options: locationOptions.array,
                })}
                onChange={(_, newValue) => {
                  setFormValue(field.name, newValue ? newValue.id : null, {
                    shouldValidate: true,
                  })
                }}
                value={
                  field.value ? locationOptions.byId.get(field.value) ?? null : null
                }
                renderInput={(params) => (
                  <TextField
                    data-testid="StepTwo-PickupLocation-TextField"
                    {...params}
                    required
                    label={pickupLocationLabel}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                    error={!!fieldState.error}
                  />
                )}
              />
              {isEmpty(locationOptions.array) && (
                <Typography
                  variant="overline"
                  sx={{ cursor: 'pointer', mt: 1, ml: 1 }}
                  onClick={() => history.push(LIST.subMenusRoutes.FACILITIES.path)}
                >
                  {translateFacilitiesTerm('facilities.addFacility')}
                </Typography>
              )}
            </>
          )}
        />
      </Box>

      {match(journeyType)
        .with(JOURNEY_TYPE.RETURN, () => null)
        .with(JOURNEY_TYPE.SINGLE, JOURNEY_TYPE.MULTIPLE, (type) => (
          <JourneysList
            control={control}
            disabled={disabled}
            unifiedLocationOptions={unifiedLocationOptions}
            journeyType={type === JOURNEY_TYPE.SINGLE ? 'single' : 'multiple'}
          />
        ))
        .exhaustive()}
    </Stack>
  )
}

export default StepTwo
