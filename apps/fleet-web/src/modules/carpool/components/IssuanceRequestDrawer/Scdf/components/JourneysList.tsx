import { Box, Button, Divider } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import {
  useFieldArray,
  useFormState,
  type Control,
  type FieldArrayPath,
} from 'react-hook-form'

import IntlTypography from 'src/util-components/IntlTypography'

import type { BookingFormSchema } from '../schema'
import type { UnifiedLocationOption } from '../types'
import DraggableJourney from './DraggableJourney'

type JourneysListProps = {
  control: Control<BookingFormSchema>
  disabled: boolean
  unifiedLocationOptions: {
    array: Array<UnifiedLocationOption>
    byId: Map<string, UnifiedLocationOption>
  }
  journeyType: 'single' | 'multiple'
}

const JourneysList = ({
  control,
  disabled,
  unifiedLocationOptions,
  journeyType,
}: JourneysListProps) => {
  const { fields, append, remove, move } = useFieldArray({
    control,
    name: 'journeys' as FieldArrayPath<BookingFormSchema>,
  })

  const { errors } = useFormState({ control })

  const addJourney = () => {
    // Add empty journey with default location type
    append({ type: 'location', id: null })
  }

  const moveJourney = (dragIndex: number, hoverIndex: number) => {
    move(dragIndex, hoverIndex)
  }

  const removeJourney = (index: number) => {
    remove(index)
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Box>
        {fields.map((field, index) => (
          <DraggableJourney
            key={field.id}
            index={index}
            control={control}
            moveJourney={moveJourney}
            removeJourney={removeJourney}
            disabled={disabled}
            unifiedLocationOptions={unifiedLocationOptions}
            canRemove={fields.length > 1}
            canMove={fields.length > 1}
          />
        ))}

        {journeyType === 'multiple' && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              my: 1,
            }}
          >
            <Divider sx={{ flex: 1 }} />
            <Button
              data-testid="JourneysList-AddLocation-Button"
              startIcon={<AddIcon />}
              onClick={addJourney}
              variant="text"
              size="small"
              disabled={disabled}
              sx={{
                color: 'primary.main',
                textTransform: 'uppercase',
                fontWeight: 500,
                mx: 2,
                '&:hover': {
                  backgroundColor: 'transparent',
                },
              }}
            >
              Add Location
            </Button>
            <Divider sx={{ flex: 1 }} />
          </Box>
        )}
        {errors.journeys && (
          <IntlTypography
            data-testid="JourneysList-Error"
            sx={{ pt: 1, color: 'error.main' }}
            msgProps={{ id: errors.journeys.message ?? '' }}
          />
        )}
      </Box>
    </DndProvider>
  )
}

export default JourneysList
