import { useCallback, useMemo } from 'react'
import {
  Autocomplete,
  Box,
  TextField,
  Typography,
  type AutocompleteProps,
} from '@karoo-ui/core'
import {
  Controller,
  useController,
  type Control,
  type FieldPath,
  type FieldValues,
  type PathValue,
} from 'react-hook-form'
import type { Except } from 'type-fest'

import type { ClientUserId } from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useUsersQuery, type UseUsersQueryData } from 'src/modules/api/useUsersQuery'
import { ctIntl } from 'src/util-components/ctIntl'

export type UserAutocompleteOption = {
  value: ClientUserId
  label: string
  email?: string | null
}

export type UserAutocompleteProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  control: Control<TFieldValues>
  name: TName
  label: string
  disabled?: boolean
  required?: boolean
  helperText?: string
  userFilterMethod?: (user: UseUsersQueryData['users'][number]) => boolean
} & Except<
  AutocompleteProps<UserAutocompleteOption, false, false, false>,
  | 'onChange'
  | 'options'
  | 'value'
  | 'filterOptions'
  | 'renderInput'
  | 'disabled'
  | 'loading'
  | 'size'
>

/**
 * Reusable UserAutocomplete component for selecting users from the system

 */
function UserAutocompleteRHF<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  disabled = false,
  required = false,
  helperText,
  userFilterMethod = () => true,
  ...rest
}: UserAutocompleteProps<TFieldValues, TName>) {
  const usersQuery = useUsersQuery()
  const {
    field: { value: selectedClientUserId },
  } = useController({ name, control })

  const userOptions = useMemo(() => {
    const array: Array<UserAutocompleteOption> = []
    const byId = new Map<ClientUserId, UserAutocompleteOption>()

    if (usersQuery.status === 'success' && usersQuery.data?.users) {
      for (const user of usersQuery.data.users) {
        if (
          (user.status === 'active' && userFilterMethod(user)) ||
          selectedClientUserId === user.id // if the user is selected, we should show it even if it's inactive
        ) {
          const option: UserAutocompleteOption = {
            value: user.id,
            label: user.username,
            email: user.email,
          }

          array.push(option)
          byId.set(user.id, option)
        }
      }
    }

    return { array, byId }

    // NOTE: if the user exists initially, we just add it to the options
    // and would not remove it when user deselects it
    // eslint-disable-next-line react-hooks/react-compiler, react-hooks/exhaustive-deps
  }, [usersQuery.status, usersQuery.data?.users, userFilterMethod])

  const filterUserOptions = useCallback(
    (options: Array<UserAutocompleteOption>, searchTerm: string) => {
      if (!searchTerm) return options

      const lowercaseSearchTerm = searchTerm.toLowerCase()
      return options.filter(
        (option) =>
          (option.label && option.label.toLowerCase().includes(lowercaseSearchTerm)) ||
          (option.email && option.email.toLowerCase().includes(lowercaseSearchTerm)),
      )
    },
    [],
  )

  return (
    <Controller
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <Autocomplete<UserAutocompleteOption>
          {...rest}
          disabled={disabled}
          loading={usersQuery.status === 'pending'}
          size="small"
          {...getAutocompleteVirtualizedProps({
            options: userOptions.array,
            getRowHeight: (option) => (option.email ? 48 : 36),
            renderRowSingleItemContent: ({ option }) => (
              <Box sx={{ py: 1 }}>
                <Typography
                  variant="body1"
                  sx={{ mb: 0 }}
                >
                  {option.label}
                </Typography>
                {option.email && (
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ display: 'block', mt: 0 }}
                  >
                    {option.email}
                  </Typography>
                )}
              </Box>
            ),
          })}
          onChange={(_, newValue) => {
            const value = (newValue ? newValue.value : null) as PathValue<
              TFieldValues,
              TName
            >
            field.onChange(value)
          }}
          value={
            field.value
              ? userOptions.byId.get(field.value as unknown as ClientUserId) ?? null
              : null
          }
          filterOptions={(options, params) =>
            filterUserOptions(options, params.inputValue)
          }
          renderInput={(params) => (
            <TextField
              {...params}
              required={required}
              label={label}
              helperText={
                fieldState.error?.message
                  ? ctIntl.formatMessage({
                      id: fieldState.error.message,
                    })
                  : helperText
              }
              error={!!fieldState.error}
            />
          )}
        />
      )}
    />
  )
}

export default UserAutocompleteRHF
