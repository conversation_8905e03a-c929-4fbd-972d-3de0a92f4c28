// eslint-disable typescript/no-require-imports
require('dotenv').config()
const { join } = require('node:path')
const rspack = require('@rspack/core')
const { defineConfig } = require('@rspack/cli')
const ReactRefreshPlugin = require('@rspack/plugin-react-refresh')
// const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin')
const {
  resolveJSAndTS,
  getModuleJSAndTSRules,
  plugins: { ignoreMomentLocalesPlugin },
} = require('./webpack.config.base.cjs')
const packageConfig = require('../../package.json')
const { z } = require('zod')
const { version } = packageConfig

const schema = z.object({
  NODE_ENV: z.enum([
    'development',
    'production',
    'cypress-component-test-run',
    'cypress-component-test-open',
  ]),
})

// This will throw an error in case the schema is not valid
const { NODE_ENV } = schema.parse(process.env)

/**
 *
 * @param {typeof NODE_ENV} env
 */
const NODE_ENV_equals = (env) => {
  // Throw if we are trying to compare with an invalid value
  const validEnv = schema.shape.NODE_ENV.parse(env)
  return NODE_ENV === validEnv
}

const isCypressEnv = process.env.CYPRESS === 'true'
const useReactRefresh = NODE_ENV_equals('development') && !isCypressEnv

const mode = NODE_ENV_equals('development') ? 'development' : 'production'
module.exports = defineConfig({
  target: ['browserslist'],
  mode,
  entry: './src/index.tsx',
  stats: 'errors-warnings',
  output: {
    ...(NODE_ENV_equals('production') && {
      path: join(process.cwd(), 'dist/apps/fleet-web'),
      filename: 'static/js/[name].[contenthash].js',
      chunkFilename: 'static/js/[name].[contenthash].chunk.js',
      // Css
      cssFilename: 'static/css/[name].[contenthash].css',
      cssChunkFilename: 'static/css/[name].[contenthash].chunk.css',
    }),
    // On rspack 0.7.1 "HMR is not implemented for module chunk format yet". Waiting for that to be implemented before we can enable this
    // module: true,
    publicPath: '/',
  },
  experiments: {
    css: true,
    lazyCompilation:
      NODE_ENV_equals('development') && process.env.DEVELOPMENT_ENV !== 'docker'
        ? {
            // Since we have an SPA, we have a single entry point so we should compile "entries" immediately.
            entries: false,
          }
        : false,
  },
  context: __dirname,
  optimization: {
    splitChunks: {
      // Last tested with "@rspack/core": "0.6.2"
      // If we don't do this, rspack will create one main (minimized) 19MB chunk. That's just not good!
      // Chunks: 'initial' produces one main 6.5MB chunk, a vendor 12M chunk and other small ones (just like webpack)
      // We should try to remove this once rspack provides a better default
      chunks: NODE_ENV_equals('production') ? 'initial' : undefined,
    },
  },
  devtool: (() => {
    if (NODE_ENV_equals('cypress-component-test-open')) {
      return 'eval-cheap-module-source-map' // perf reasons
    }
    if (NODE_ENV_equals('cypress-component-test-run')) {
      return 'hidden-source-map'
    }

    return NODE_ENV_equals('development')
      ? 'cheap-module-source-map'
      : 'hidden-source-map'
  })(),
  devServer: NODE_ENV_equals('development')
    ? {
        historyApiFallback: true,
        host: '0.0.0.0',
        port: 8080,
        hot: true,
        liveReload: false,
        static: __dirname,
        devMiddleware: { publicPath: '/' },
        client: { overlay: false },
      }
    : undefined,
  resolve: resolveJSAndTS,
  module: {
    rules: [
      ...getModuleJSAndTSRules({
        useReactRefresh,
        transpileNodeModules: NODE_ENV_equals('production'),
      }),
      {
        test: /\.scss$/,
        exclude: /node_modules/,
        use: [
          'postcss-loader',
          {
            loader: 'sass-loader',
            options: {
              // muting Sass warnings since we're not updating our legacy code for the new version.
              sassOptions: {
                quietDeps: true,
                logger: {
                  warn: () => {}, // suppress all warnings by doing nothing
                },
              },
            },
          },
        ],
        type: 'css',
      },
      {
        test: /\.css$/,
        type: 'css',
      },
      {
        test: /\.pdf$/,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]',
        },
      },
      {
        test: /\.svg$/,
        exclude: /assets/,
        type: 'asset/inline',
      },
      {
        test: /\.svg$/,
        include: /assets/,
        /** Deprecated plugin - Convert to imaginin svgo with "removeDimensions: true" */
        use: [
          {
            loader: 'svg-inline-loader',
            options: {
              removeTags: true,
              removingTags: ['title'],
            },
          },
        ],
      },
      {
        test: /\.(jpe?g|png|gif|ico|eot|woff|otf|ttf|woff2|webp|avif)$/,
        type: 'asset',
      },
      {
        test: /\.(mp3)(\?.*)?$/,
        type: 'asset/inline',
      },
    ],
  },
  plugins: [
    new rspack.CircularDependencyRspackPlugin({
      failOnError: true, // Should enable in the future once we fix our circular dependencies
      exclude: /node_modules/,
    }),
    ignoreMomentLocalesPlugin,
    /* Uncomment to enable typescript checking errors on the rspack terminal process */
    // nodeEnv === ENVS.DEV &&
    //   new ForkTsCheckerWebpackPlugin({
    //     typescript: {
    //       configFile: 'tsconfig.app.json',
    //       memoryLimit: 6500,
    //       /** Recommended mode when used with babel loader
    //        *  Check https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/blob/master/README.md#typescript-options
    //        */
    //       mode: 'write-references',
    //       diagnosticOptions: {
    //         /* Since babel only transpiles typescript, this plugin has to cover all types of errors (both semantic and syntactic). */
    //         syntactic: true,
    //       },
    //     },
    //   }),
    // cypress-rspack-dev-server already applies this plugin so we don't do it again to prevent conflicts
    new rspack.HtmlRspackPlugin({
      template: './index.ejs',
    }),
    new rspack.ProvidePlugin({ process: 'process' }),
    new rspack.CopyRspackPlugin({
      patterns: [
        {
          from: 'assets/customer_styling/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/img/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/templates/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/icons/favicons/*',
          to: 'assets/[name][ext]',
        },
        {
          from: 'assets/templates-delivery/**/*',
        },
      ],
    }),
    new rspack.DefinePlugin({
      ENV: {
        ...(NODE_ENV_equals('production')
          ? {
              NODE_ENV: JSON.stringify('production'),
              GA_TRACKING_ID: JSON.stringify('UA-153974304-1'),
              GMAP_API_KEY: JSON.stringify(
                process.env.STAGING
                  ? process.env.STAGING_GMAP_API_KEY
                  : process.env.GMAP_API_KEY,
              ),
              SENTRY_DSN: JSON.stringify(
                'https://<EMAIL>/1444444',
              ),
            }
          : {
              NODE_ENV: JSON.stringify('development'),
              WITH_ELD: JSON.stringify(process.env.WITH_ELD),
              ENABLE_REDUX_DEV_MIDDLEWARE: JSON.stringify(
                process.env.ENABLE_REDUX_DEV_MIDDLEWARE,
              ),
              DEVELOPMENT_ENDPOINT: JSON.stringify(
                process.env.DEVELOPMENT_ENDPOINT ||
                  `https://fleetweb${process.env.TEST_ENV || 'dev'}-${
                    process.env.DEVELOPMENT_COUNTRY
                  }.cartrack.com/jsonrpc/index.php`,
              ),
              GMAP_API_KEY: JSON.stringify(process.env.GMAP_API_KEY),
            }),
        DEPLOYMENT_ENV: JSON.stringify(process.env.DEPLOYMENT_ENV || 'unspecified'),
        APP_VERSION: JSON.stringify(version),
        HERE_MAPS_API_KEY: JSON.stringify(process.env.HERE_MAPS_API_KEY),
        RESTAPI_ENDPOINT: JSON.stringify(process.env.RESTAPI_ENDPOINT),
        CYPRESS_CT_ENV: JSON.stringify(
          NODE_ENV_equals('cypress-component-test-open') ||
            NODE_ENV_equals('cypress-component-test-run')
            ? NODE_ENV
            : null,
        ),

        /* EXPERIMENTAL FEATURES */
        FEAT_DELIVERY_REVAMP: JSON.stringify(process.env.FEAT_DELIVERY_REVAMP),
        FEAT_MIFLEET_DECIMAL_SEPARATORS: JSON.stringify(
          process.env.FEAT_MIFLEET_DECIMAL_SEPARATORS,
        ),
        NEW_LOGIN: JSON.stringify(process.env.NEW_LOGIN),
        FLAG_NEW_REDESIGN: JSON.stringify(process.env.FLAG_NEW_REDESIGN === 'true'),
      },
    }),
    useReactRefresh && new ReactRefreshPlugin(),
  ].filter(Boolean),
})
